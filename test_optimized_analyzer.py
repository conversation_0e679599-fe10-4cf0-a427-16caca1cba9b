#!/usr/bin/env python3
"""
优化后的数据分析器测试脚本
验证设备调运记录和充电完成率优化
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from datetime import datetime, timedelta
from unittest.mock import Mock
import json

from app.core.analyzer import DataAnalyzer
from app.core.calculator import ScoreCalculator


def create_mock_bus_logs():
    """创建模拟设备调运记录"""
    logs = []
    base_date = datetime(2025, 1, 1)
    
    # 模拟设备调运记录
    log_data = [
        {"date": base_date + timedelta(days=5), "type": "设备调入", "desc": "新增1台设备"},
        {"date": base_date + timedelta(days=15), "type": "设备调出", "desc": "移除1台设备"},
        {"date": base_date + timedelta(days=25), "type": "设备调入", "desc": "再次新增1台设备"},
    ]
    
    for i, log_info in enumerate(log_data):
        log = Mock()
        log.id = i + 1
        log.type = log_info["type"]
        log.bus_unique = "station_test_001"
        log.create_time = log_info["date"]
        log.connector_id = f"device_{i+1}"
        logs.append(log)
    
    return logs


def create_mock_all_orders():
    """创建包含所有状态的模拟订单数据"""
    orders = []
    base_date = datetime(2025, 1, 1)
    
    # 创建不同状态的订单
    order_statuses = [
        {"status": 4, "timelong": 7200, "count": 80},  # 完成订单
        {"status": 4, "timelong": 0, "count": 5},      # 完成但时长为0
        {"status": 5, "timelong": 0, "count": 10},     # 取消订单
        {"status": 3, "timelong": 3600, "count": 5},   # 充电中
    ]
    
    order_id = 1
    for status_info in order_statuses:
        for i in range(status_info["count"]):
            order = Mock()
            order.id = f"order_{order_id}"
            order.station_id = "station_test_001"
            order.user_id = f"user_{(order_id % 20) + 1}"
            order.person_user_id = f"person_{(order_id % 20) + 1}"  # 个人用户ID
            order.total_fees = 50.0 + (order_id % 10) * 5
            order.charge_power = 40.0 + (order_id % 15) * 2
            order.timelong = status_info["timelong"]
            order.status = status_info["status"]
            order.normal_end = 1 if status_info["status"] == 4 else 0
            order.pay_type = 1 if order_id % 5 != 4 else 2  # 80%个人用户
            order.create_time = base_date + timedelta(days=order_id % 30, hours=order_id % 24)
            orders.append(order)
            order_id += 1
    
    return orders


def test_optimized_device_count():
    """测试优化后的设备数量计算"""
    print("=" * 60)
    print("测试优化后的设备数量计算")
    print("=" * 60)
    
    # 创建模拟数据库会话
    db_mock = Mock()
    analyzer = DataAnalyzer(db_mock)
    
    # 模拟设备调运记录查询
    bus_logs = create_mock_bus_logs()
    db_mock.query.return_value.filter.return_value.order_by.return_value.all.return_value = bus_logs
    
    # 创建模拟设备数据
    devices = []
    for i in range(3):  # 初始3个设备
        device = Mock()
        device.station_id = "station_test_001"
        device.install_date = datetime(2025, 1, 1)
        device.remove_date = None
        devices.append(device)
    
    # 测试设备数量计算
    start_date = datetime(2025, 1, 1)
    end_date = datetime(2025, 1, 31)
    
    avg_device_count = analyzer._calculate_average_device_count(devices, start_date, end_date)
    
    print(f"初始设备数量: {len(devices)}")
    print(f"设备调运记录:")
    for log in bus_logs:
        print(f"  {log.create_time.strftime('%Y-%m-%d')}: {log.type}")
    print(f"计算得到的平均设备数量: {avg_device_count:.2f}")
    
    return avg_device_count


def test_optimized_completion_rate():
    """测试优化后的充电完成率计算"""
    print("\n" + "=" * 60)
    print("测试优化后的充电完成率计算")
    print("=" * 60)
    
    # 创建模拟数据库会话
    db_mock = Mock()
    analyzer = DataAnalyzer(db_mock)
    
    # 创建所有状态的订单数据
    all_orders = create_mock_all_orders()
    completed_orders = [order for order in all_orders if order.status == 4 and order.timelong > 0]
    
    # 模拟数据库查询返回所有订单
    db_mock.query.return_value.filter.return_value.all.return_value = all_orders
    
    print(f"总订单数: {len(all_orders)}")
    print(f"完成订单数 (status=4 且 timelong>0): {len(completed_orders)}")
    
    # 按状态统计
    status_counts = {}
    for order in all_orders:
        status = order.status
        if status not in status_counts:
            status_counts[status] = 0
        status_counts[status] += 1
    
    print("订单状态分布:")
    for status, count in status_counts.items():
        status_name = {3: "充电中", 4: "完成", 5: "取消"}.get(status, f"状态{status}")
        print(f"  {status_name}: {count}个")
    
    # 测试充电完成率计算
    start_date = datetime(2025, 1, 1)
    end_date = datetime(2025, 1, 31)
    
    completion_rate = analyzer._calculate_completion_rate_from_orders(
        completed_orders, "station_test_001", start_date, end_date
    )
    
    print(f"计算得到的充电完成率: {completion_rate}%")
    
    # 验证计算正确性
    expected_rate = (len(completed_orders) / len(all_orders)) * 100
    print(f"预期充电完成率: {expected_rate:.2f}%")
    
    return completion_rate


def test_complete_workflow():
    """测试完整的优化工作流"""
    print("\n" + "=" * 60)
    print("测试完整的优化工作流")
    print("=" * 60)
    
    # 创建模拟数据库会话
    db_mock = Mock()
    analyzer = DataAnalyzer(db_mock)
    
    # 设置模拟查询返回
    bus_logs = create_mock_bus_logs()
    all_orders = create_mock_all_orders()
    completed_orders = [order for order in all_orders if order.status == 4 and order.timelong > 0]
    
    # 配置数据库查询模拟
    def mock_query_side_effect(*args):
        mock_query = Mock()
        mock_query.filter.return_value = mock_query
        mock_query.order_by.return_value = mock_query
        
        # 根据查询类型返回不同数据
        if hasattr(args[0], '__name__') and 'SysBusLog' in str(args[0]):
            mock_query.all.return_value = bus_logs
        else:
            mock_query.all.return_value = all_orders
        
        return mock_query
    
    db_mock.query.side_effect = mock_query_side_effect
    
    # 创建完整的测试数据
    station = Mock()
    station.id = "station_test_001"
    station.name = "优化测试充电站"
    station.city_name = "北京市"
    
    devices = []
    for i in range(3):
        device = Mock()
        device.station_id = "station_test_001"
        device.install_date = datetime(2025, 1, 1)
        device.remove_date = None
        devices.append(device)
    
    raw_data = {
        "station": station,
        "devices": devices,
        "orders": completed_orders,  # 只传入完成的订单
        "period": {
            "start_date": datetime(2025, 1, 1),
            "end_date": datetime(2025, 1, 31),
            "days": 31
        }
    }
    
    # 计算各维度指标
    operation_metrics = analyzer.calculate_operation_metrics(raw_data)
    service_metrics = analyzer.calculate_service_metrics(raw_data)
    stability_metrics = analyzer.calculate_stability_metrics(raw_data)
    
    print("\n📊 优化后的运营效率指标:")
    for key, value in operation_metrics.items():
        print(f"  {key}: {value}")
    
    print("\n🎯 优化后的服务质量指标:")
    for key, value in service_metrics.items():
        print(f"  {key}: {value}")
    
    print("\n📈 优化后的稳定性指标:")
    for key, value in stability_metrics.items():
        print(f"  {key}: {value}")
    
    # 计算综合评分
    calculator = ScoreCalculator()
    score_result = calculator.calculate_total_score(
        operation_metrics,
        service_metrics,
        stability_metrics
    )
    
    print(f"\n🏆 优化后的综合评分:")
    print(f"  总分: {score_result['total_score']}")
    print(f"  等级: {score_result['grade']}")
    
    return {
        "operation_metrics": operation_metrics,
        "service_metrics": service_metrics,
        "stability_metrics": stability_metrics,
        "score_result": score_result
    }


def main():
    """主函数"""
    print("🚀 开始优化后的评分算法测试")
    
    try:
        # 测试设备数量计算优化
        avg_device_count = test_optimized_device_count()
        
        # 测试充电完成率计算优化
        completion_rate = test_optimized_completion_rate()
        
        # 测试完整工作流
        results = test_complete_workflow()
        
        # 保存结果
        optimization_results = {
            "timestamp": datetime.now().isoformat(),
            "optimizations": {
                "device_count_calculation": {
                    "method": "基于设备调运记录表(sys_bus_log)",
                    "average_device_count": avg_device_count
                },
                "completion_rate_calculation": {
                    "method": "避免重复查询，直接使用传入订单数据",
                    "completion_rate": completion_rate
                }
            },
            "complete_test_results": results
        }
        
        with open("optimization_test_results.json", "w", encoding="utf-8") as f:
            json.dump(optimization_results, f, ensure_ascii=False, indent=2)
        
        print(f"\n💾 优化测试结果已保存到: optimization_test_results.json")
        print("\n✅ 所有优化测试完成！")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
