#!/usr/bin/env python3
"""
修正后的数据分析器测试脚本
基于sys_bus_log注释内容修正计算方式
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from datetime import datetime, timedelta
from unittest.mock import Mock
import json

from app.core.analyzer import DataAnalyzer
from app.core.calculator import ScoreCalculator


def create_mock_station():
    """创建模拟场站数据"""
    station = Mock()
    station.id = "station_test_001"
    station.name = "北京测试充电站"
    return station


def create_mock_corrected_bus_logs():
    """
    创建修正后的设备调运记录
    根据注释：
    - type = 'update_device' 代表调动设备
    - bus_unique = 设备号（等同于pile_device的name）
    - old_content = 旧场站名称
    - new_content = 新场站名称
    """
    logs = []
    base_date = datetime(2025, 1, 1)
    station_name = "北京测试充电站"
    
    # 模拟设备调运记录
    log_data = [
        # 历史记录（统计期开始前）
        {"date": base_date - timedelta(days=30), "device": "device_001", "old": "上海充电站", "new": station_name},
        {"date": base_date - timedelta(days=25), "device": "device_002", "old": "广州充电站", "new": station_name},
        {"date": base_date - timedelta(days=20), "device": "device_003", "old": "深圳充电站", "new": station_name},
        
        # 统计期内的调运记录
        {"date": base_date + timedelta(days=5), "device": "device_004", "old": "杭州充电站", "new": station_name},  # 调入
        {"date": base_date + timedelta(days=15), "device": "device_002", "old": station_name, "new": "天津充电站"},  # 调出
        {"date": base_date + timedelta(days=25), "device": "device_005", "old": "南京充电站", "new": station_name},  # 调入
    ]
    
    for i, log_info in enumerate(log_data):
        log = Mock()
        log.id = i + 1
        log.type = "update_device"
        log.bus_unique = log_info["device"]  # 设备号
        log.old_content = log_info["old"]    # 旧场站名称
        log.new_content = log_info["new"]    # 新场站名称
        log.create_time = log_info["date"]
        log.operator = "system"
        logs.append(log)
    
    return logs


def test_corrected_device_count():
    """测试修正后的设备数量计算"""
    print("=" * 60)
    print("测试修正后的设备数量计算")
    print("=" * 60)
    
    # 创建模拟数据库会话
    db_mock = Mock()
    analyzer = DataAnalyzer(db_mock)
    
    # 创建模拟场站
    station = create_mock_station()
    
    # 创建设备调运记录
    bus_logs = create_mock_corrected_bus_logs()
    
    # 模拟数据库查询
    def mock_query_side_effect(*args):
        mock_query = Mock()
        mock_query.filter = Mock(return_value=mock_query)
        mock_query.order_by = Mock(return_value=mock_query)
        mock_query.first = Mock(return_value=station)
        
        # 根据查询条件返回不同的调运记录
        def mock_all():
            # 这里简化处理，实际应该根据filter条件筛选
            return bus_logs
        
        mock_query.all = mock_all
        return mock_query
    
    db_mock.query.side_effect = mock_query_side_effect
    
    # 创建模拟设备数据
    devices = []
    for i in range(3):  # 当前3个设备
        device = Mock()
        device.station_id = "station_test_001"
        device.name = f"device_{i+1:03d}"
        devices.append(device)
    
    # 测试设备数量计算
    start_date = datetime(2025, 1, 1)
    end_date = datetime(2025, 1, 31)
    
    print(f"场站名称: {station.name}")
    print(f"当前设备数量: {len(devices)}")
    print(f"统计期间: {start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')}")
    
    print("\n设备调运记录:")
    for log in bus_logs:
        direction = "调入" if log.new_content == station.name else "调出"
        if log.create_time >= start_date and log.create_time <= end_date:
            period = "统计期内"
        else:
            period = "历史记录"
        print(f"  {log.create_time.strftime('%Y-%m-%d')}: {log.bus_unique} {direction} ({period})")
    
    avg_device_count = analyzer._calculate_average_device_count(devices, start_date, end_date)
    
    print(f"\n计算结果:")
    print(f"  平均设备数量: {avg_device_count:.2f}")
    
    # 手动验证计算逻辑
    print(f"\n手动验证:")
    print(f"  统计期开始前调入设备: 3台 (device_001, device_002, device_003)")
    print(f"  统计期内调入设备: 2台 (device_004, device_005)")
    print(f"  统计期内调出设备: 1台 (device_002)")
    print(f"  预期结果: 初始3台 + 调入2台 - 调出1台 = 4台")
    
    return avg_device_count


def test_complete_corrected_workflow():
    """测试完整的修正工作流"""
    print("\n" + "=" * 60)
    print("测试完整的修正工作流")
    print("=" * 60)
    
    # 创建模拟数据库会话
    db_mock = Mock()
    analyzer = DataAnalyzer(db_mock)
    
    # 创建模拟数据
    station = create_mock_station()
    bus_logs = create_mock_corrected_bus_logs()
    
    # 模拟订单数据
    orders = []
    for i in range(80):  # 80个完成订单
        order = Mock()
        order.id = f"order_{i+1}"
        order.station_id = "station_test_001"
        order.person_user_id = f"person_{(i % 15) + 1}"  # 15个不同用户
        order.total_fees = 60.0 + (i % 8) * 5
        order.charge_power = 45.0 + (i % 12) * 3
        order.timelong = 7200 + (i % 15) * 300
        order.status = 4
        order.normal_end = 1
        order.pay_type = 1
        order.create_time = datetime(2025, 1, 1) + timedelta(days=i % 30, hours=i % 24)
        orders.append(order)
    
    # 配置数据库查询模拟
    def mock_query_side_effect(*args):
        mock_query = Mock()
        mock_query.filter = Mock(return_value=mock_query)
        mock_query.order_by = Mock(return_value=mock_query)
        mock_query.first = Mock(return_value=station)
        mock_query.all = Mock(return_value=bus_logs)
        return mock_query
    
    db_mock.query.side_effect = mock_query_side_effect
    
    # 创建设备数据
    devices = []
    for i in range(4):  # 4个设备
        device = Mock()
        device.station_id = "station_test_001"
        device.name = f"device_{i+1:03d}"
        devices.append(device)
    
    # 创建完整测试数据
    raw_data = {
        "station": station,
        "devices": devices,
        "orders": orders,
        "period": {
            "start_date": datetime(2025, 1, 1),
            "end_date": datetime(2025, 1, 31),
            "days": 31
        }
    }
    
    # 计算各维度指标
    operation_metrics = analyzer.calculate_operation_metrics(raw_data)
    service_metrics = analyzer.calculate_service_metrics(raw_data)
    stability_metrics = analyzer.calculate_stability_metrics(raw_data)
    
    print("\n📊 修正后的运营效率指标:")
    for key, value in operation_metrics.items():
        print(f"  {key}: {value}")
    
    print("\n🎯 修正后的服务质量指标:")
    for key, value in service_metrics.items():
        print(f"  {key}: {value}")
    
    print("\n📈 修正后的稳定性指标:")
    for key, value in stability_metrics.items():
        print(f"  {key}: {value}")
    
    # 计算综合评分
    calculator = ScoreCalculator()
    score_result = calculator.calculate_total_score(
        operation_metrics,
        service_metrics,
        stability_metrics
    )
    
    print(f"\n🏆 修正后的综合评分:")
    print(f"  总分: {score_result['total_score']}")
    print(f"  等级: {score_result['grade']}")
    
    return {
        "operation_metrics": operation_metrics,
        "service_metrics": service_metrics,
        "stability_metrics": stability_metrics,
        "score_result": score_result,
        "device_count_analysis": {
            "current_devices": len(devices),
            "calculated_average": operation_metrics.get("device_utilization", 0) * 31 * 4 / 80 if operation_metrics.get("device_utilization") else 0
        }
    }


def main():
    """主函数"""
    print("🚀 开始修正后的评分算法测试")
    print("基于sys_bus_log注释内容修正计算方式")
    
    try:
        # 测试修正后的设备数量计算
        avg_device_count = test_corrected_device_count()
        
        # 测试完整工作流
        results = test_complete_corrected_workflow()
        
        # 保存结果
        corrected_results = {
            "timestamp": datetime.now().isoformat(),
            "corrections_applied": {
                "sys_bus_log_interpretation": {
                    "type": "update_device (代表调动设备)",
                    "bus_unique": "设备号 (等同于pile_device的name)",
                    "old_content": "旧场站名称 (等同于pile_station的name)",
                    "new_content": "新场站名称 (等同于pile_station的name)",
                    "create_time": "调动时间"
                },
                "device_count_calculation": {
                    "method": "基于场站名称匹配调运记录",
                    "logic": "new_content=场站名(调入+1), old_content=场站名(调出-1)",
                    "average_device_count": avg_device_count
                }
            },
            "complete_test_results": results
        }
        
        with open("corrected_test_results.json", "w", encoding="utf-8") as f:
            json.dump(corrected_results, f, ensure_ascii=False, indent=2)
        
        print(f"\n💾 修正测试结果已保存到: corrected_test_results.json")
        print("\n✅ 所有修正测试完成！")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
