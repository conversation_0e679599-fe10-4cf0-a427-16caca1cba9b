"""
评分相关数据模型
"""
from sqlalchemy import Column, BigInteger, String, Integer, DateTime, Float, Text, Enum, JSON
from sqlalchemy.sql import func
from app.database import Base
import enum


class CalculationStatus(enum.Enum):
    """计算状态枚举"""
    PENDING = "PENDING"
    RUNNING = "RUNNING"
    SUCCESS = "SUCCESS"
    FAILED = "FAILED"
    CANCELLED = "CANCELLED"


class TriggerType(enum.Enum):
    """触发类型枚举"""
    MANUAL = "MANUAL"
    SCHEDULED = "SCHEDULED"
    API = "API"


class TaskType(enum.Enum):
    """任务类型枚举"""
    FULL = "FULL"
    INCREMENTAL = "INCREMENTAL"
    SINGLE = "SINGLE"


class StationScore(Base):
    """场站评分主表"""
    __tablename__ = "station_scores"
    
    id = Column(BigInteger, primary_key=True, autoincrement=True, comment="主键ID")
    station_id = Column(String(64), nullable=False, comment="场站ID")
    station_name = Column(String(200), nullable=False, comment="场站名称")
    city_name = Column(String(300), nullable=False, comment="城市名称")
    
    # 评分数据
    total_score = Column(Float(5, 2), nullable=False, comment="总评分 0-100")
    grade = Column(String(1), nullable=False, comment="评分等级 S/A/B/C/D")
    
    # 分维度评分
    operation_score = Column(Float(5, 2), nullable=False, comment="运营效率评分")
    service_score = Column(Float(5, 2), nullable=False, comment="服务质量评分")
    stability_score = Column(Float(5, 2), nullable=False, comment="稳定性评分")
    
    # 排名信息
    city_rank = Column(Integer, comment="城市排名")
    national_rank = Column(Integer, comment="全国排名")
    
    # 统计周期
    stat_period = Column(String(20), nullable=False, comment="统计周期 YYYY-MM")
    stat_start_date = Column(DateTime, nullable=False, comment="统计开始日期")
    stat_end_date = Column(DateTime, nullable=False, comment="统计结束日期")
    
    # 基础统计数据
    total_orders = Column(Integer, nullable=False, default=0, comment="总订单数")
    completed_orders = Column(Integer, nullable=False, default=0, comment="完成订单数")
    total_revenue = Column(Float(12, 2), nullable=False, default=0, comment="总收益(元)")
    device_count = Column(Integer, nullable=False, default=0, comment="设备数量")
    operating_days = Column(Integer, nullable=False, default=0, comment="运营天数")
    unique_users = Column(Integer, nullable=False, default=0, comment="独立用户数")  #不需要，没有这种数据
    
    # 系统字段
    create_time = Column(DateTime, default=func.current_timestamp(), comment="创建时间")
    update_time = Column(DateTime, default=func.current_timestamp(), 
                        onupdate=func.current_timestamp(), comment="更新时间")

    def __repr__(self):
        return f"<StationScore(station_id='{self.station_id}', period='{self.stat_period}', score={self.total_score})>"


class StationScoreDetail(Base):
    """场站评分详细指标表"""
    __tablename__ = "station_score_details"
    
    id = Column(BigInteger, primary_key=True, autoincrement=True, comment="主键ID")
    station_id = Column(String(64), nullable=False, comment="场站ID")
    stat_period = Column(String(20), nullable=False, comment="统计周期")
    
    # 运营效率指标 (50%权重)
    daily_revenue_rate = Column(Float(10, 2), comment="日均收益率(元/天)")
    device_utilization = Column(Float(5, 2), comment="设备利用率(%)")
    revenue_per_device = Column(Float(10, 2), comment="单设备产出(元/设备)")
    completion_rate = Column(Float(5, 2), comment="充电完成率(%)")
    
    # 服务质量指标 (30%权重)
    avg_charge_time = Column(Float(6, 2), comment="平均充电时长(小时)")
    avg_charge_power = Column(Float(8, 4), comment="平均充电量(kWh)")
    user_retention_rate = Column(Float(5, 2), comment="用户复购率(%)")
    failure_rate = Column(Float(5, 2), comment="故障率(%)")
    
    # 稳定性指标 (20%权重)
    order_growth_trend = Column(Float(6, 2), comment="订单增长趋势(%)")
    revenue_stability = Column(Float(5, 2), comment="收益稳定性(%)")
    operation_continuity = Column(Float(5, 2), comment="运营连续性(%)")
    device_health_rate = Column(Float(5, 2), comment="设备健康度(%)")
    time_balance_index = Column(Float(5, 2), comment="时段均衡性指数")
    
    # 基础统计数据
    total_orders = Column(Integer, comment="总订单数")
    completed_orders = Column(Integer, comment="完成订单数")
    failed_orders = Column(Integer, comment="异常订单数")
    total_revenue = Column(Float(12, 2), comment="总收益(元)")
    operating_days = Column(Integer, comment="运营天数")
    device_count = Column(Integer, comment="设备数量")
    unique_users = Column(Integer, comment="独立用户数")
    repeat_users = Column(Integer, comment="复购用户数")
    
    # 时段分析数据
    peak_hour_orders = Column(Integer, comment="高峰期订单数")
    off_peak_orders = Column(Integer, comment="非高峰期订单数")
    weekend_orders = Column(Integer, comment="周末订单数")
    weekday_orders = Column(Integer, comment="工作日订单数")
    
    create_time = Column(DateTime, default=func.current_timestamp(), comment="创建时间")

    def __repr__(self):
        return f"<StationScoreDetail(station_id='{self.station_id}', period='{self.stat_period}')>"


class StationScoreTrend(Base):
    """场站评分历史趋势表"""
    __tablename__ = "station_score_trends"
    
    id = Column(BigInteger, primary_key=True, autoincrement=True, comment="主键ID")
    station_id = Column(String(64), nullable=False, comment="场站ID")
    stat_period = Column(String(20), nullable=False, comment="统计周期")
    
    # 评分变化趋势
    total_score = Column(Float(5, 2), nullable=False, comment="当期总评分")
    prev_total_score = Column(Float(5, 2), comment="上期总评分")
    score_change = Column(Float(6, 2), comment="评分变化值")
    score_change_rate = Column(Float(5, 2), comment="评分变化率(%)")
    
    # 排名变化趋势
    city_rank = Column(Integer, comment="当期城市排名")
    prev_city_rank = Column(Integer, comment="上期城市排名")
    city_rank_change = Column(Integer, comment="城市排名变化")
    
    national_rank = Column(Integer, comment="当期全国排名")
    prev_national_rank = Column(Integer, comment="上期全国排名")
    national_rank_change = Column(Integer, comment="全国排名变化")
    
    # 等级变化
    grade = Column(String(1), nullable=False, comment="当期等级")
    prev_grade = Column(String(1), comment="上期等级")
    grade_changed = Column(Integer, default=0, comment="等级是否变化 1:是 0:否")
    
    # 各维度变化趋势
    operation_score_change = Column(Float(6, 2), comment="运营效率评分变化")
    service_score_change = Column(Float(6, 2), comment="服务质量评分变化")
    stability_score_change = Column(Float(6, 2), comment="稳定性评分变化")
    
    # 关键指标变化
    revenue_change_rate = Column(Float(6, 2), comment="收益变化率")
    order_change_rate = Column(Float(6, 2), comment="订单量变化率")
    user_change_rate = Column(Float(6, 2), comment="用户数变化率")
    
    create_time = Column(DateTime, default=func.current_timestamp(), comment="创建时间")

    def __repr__(self):
        return f"<StationScoreTrend(station_id='{self.station_id}', period='{self.stat_period}', change={self.score_change})>"


class ScoreCalculationLog(Base):
    """评分计算日志表"""
    __tablename__ = "score_calculation_logs"
    
    id = Column(BigInteger, primary_key=True, autoincrement=True, comment="主键ID")
    batch_id = Column(String(64), nullable=False, comment="批次ID")
    stat_period = Column(String(20), nullable=False, comment="统计周期")
    
    # 任务信息
    task_type = Column(Enum(TaskType), default=TaskType.FULL, comment="任务类型")
    trigger_type = Column(Enum(TriggerType), default=TriggerType.SCHEDULED, comment="触发方式")
    trigger_user = Column(String(64), comment="触发用户")
    
    # 执行统计
    total_stations = Column(Integer, nullable=False, default=0, comment="总场站数")
    success_count = Column(Integer, nullable=False, default=0, comment="成功计算数")
    failed_count = Column(Integer, nullable=False, default=0, comment="失败计算数")
    skipped_count = Column(Integer, nullable=False, default=0, comment="跳过计算数")
    
    # 时间信息
    start_time = Column(DateTime, comment="开始时间")
    end_time = Column(DateTime, comment="结束时间")
    duration_seconds = Column(Integer, comment="耗时(秒)")
    
    # 状态信息
    status = Column(Enum(CalculationStatus), default=CalculationStatus.PENDING, comment="执行状态")
    progress_percentage = Column(Float(5, 2), default=0, comment="进度百分比")
    error_message = Column(Text, comment="错误信息")
    
    # 结果统计
    result_summary = Column(JSON, comment="结果摘要(JSON格式)")
    
    create_time = Column(DateTime, default=func.current_timestamp(), comment="创建时间")
    update_time = Column(DateTime, default=func.current_timestamp(), 
                        onupdate=func.current_timestamp(), comment="更新时间")

    def __repr__(self):
        return f"<ScoreCalculationLog(batch_id='{self.batch_id}', status='{self.status.value}')>"
