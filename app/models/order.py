"""
订单和用户相关数据模型
"""
from sqlalchemy import Column, BigInteger, String, Integer, DateTime, Float, Text
from sqlalchemy.sql import func
from app.database import Base


class PileOrder(Base):
    """订单信息表"""
    __tablename__ = "pile_order"
    
    id = Column(BigInteger, primary_key=True, autoincrement=True, comment="订单id")
    no = Column(String(20), comment="订单编号")
    pay_method = Column(Integer, comment="1微信预支付 2支付宝预支付 3余额支付 4充电卡 5余额充电卡组合支付")
    pay_account = Column(String(40), comment="支付账户")
    pay_type = Column(Integer, default=1, comment="支付类型。1：个人，2：企业")
    person_user_id = Column(BigInteger, comment="个人用户ID")
    operator_id = Column(String(40), comment="运营商ID")
    station_id = Column(String(40), comment="充电站ID")
    pile_no = Column(String(40), comment="充电桩编号")
    gun_id = Column(String(40), comment="充电枪id")
    start_time = Column(DateTime, comment="开始时间")
    end_time = Column(DateTime, comment="完成时间")
    start_soc = Column(Integer, comment="被充汽车开始soc")
    end_soc = Column(Integer, comment="被充汽车结束soc")
    timelong = Column(Float, comment="时长")
    charge_power = Column(Float(8, 4), comment="用电量")
    charge_fees = Column(Float(8, 4), comment="充电费用")
    server_fees = Column(Float(8, 4), comment="服务费")
    capital_fees = Column(Float(8, 2), comment="本金费用")
    give_fees = Column(Float(8, 2), comment="赠送费用")
    total_fees = Column(Float(8, 2), comment="总费用")
    status = Column(Integer, comment="订单状态:0:预约中 1:未接单 2:准备充电 3:充电中 4:充电完成 5:已取消")
    pay_time = Column(DateTime, comment="支付时间")
    pay_status = Column(Integer, comment="支付状态:0、未支付 1、已支付")
    overflow_policy = Column(Integer, comment="充满电策略")
    overflow_para = Column(Integer, comment="充满电策略参数")
    normal_end = Column(Integer, comment="是否正常结束 根据订单结算上报的情况来区分")
    vin = Column(String(40), comment="被充汽车车架号")
    chargeable_capacity = Column(Float(10, 2), comment="可充电量")
    create_time = Column(DateTime, default=func.current_timestamp(), comment="创建时间")
    update_time = Column(DateTime, default=func.current_timestamp(), 
                        onupdate=func.current_timestamp(), comment="更新时间")

    def __repr__(self):
        return f"<PileOrder(id={self.id}, no='{self.no}', station_id='{self.station_id}')>"


class PileOrderSnapshot(Base):
    """订单快照表"""
    __tablename__ = "pile_order_snapshot"
    
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    order_id = Column(BigInteger, comment="订单id")
    order_no = Column(String(64), comment="订单编号")
    operator_order_no = Column(String(64), comment="运营商订单编号")
    pay_type = Column(Integer, comment="支付类型。1：个人，2：企业")
    pay_method = Column(Integer, default=3, comment="1、微信预支付 2、支付宝预支付 3、余额支付")
    svcard_no = Column(String(64), comment="储值卡编码")
    user_phone = Column(String(64), comment="用户手机号")
    person_user_id = Column(BigInteger, comment="个人用户id")
    operator_id = Column(String(64), comment="运营商id")
    station_id = Column(BigInteger, comment="场站ID")
    station_name = Column(String(255), comment="场站名称")
    pile_no = Column(String(64), comment="充电桩编号")
    start_time = Column(DateTime, comment="开始时间")
    end_time = Column(DateTime, comment="完成时间")
    timelong = Column(Float, comment="时长")
    start_soc = Column(Integer, comment="被充汽车的开始soc")
    end_soc = Column(Integer, comment="被充汽车结束soc")
    charge_power = Column(Float(8, 4), comment="用电量")
    charge_fees = Column(Float(8, 4), comment="充电费")
    server_fees = Column(Float(8, 4), comment="服务费")
    total_fees = Column(Float(8, 2), comment="总费用")
    status = Column(Integer, comment="订单状态:0:预约中 1:未接单 2:准备充电 3:充电中 4:充电完成 5:已取消")
    overflow_policy = Column(Integer, comment="充满电策略")
    normal_end = Column(Integer, comment="是否正常结束 根据订单结算上报的情况来区分")
    vin = Column(String(64), comment="被充汽车车架号")
    create_time = Column(DateTime, comment="创建时间")
    add_time = Column(DateTime, default=func.current_timestamp(), comment="增加时间")

    def __repr__(self):
        return f"<PileOrderSnapshot(id={self.id}, order_no='{self.order_no}', station_id='{self.station_id}')>"


class AppUser(Base):
    """app用户"""
    __tablename__ = "app_user"
    
    id = Column(BigInteger, primary_key=True, autoincrement=True, comment="id")
    name = Column(String(60), comment="用户名")
    active_code = Column(String(60), comment="激活代码")
    svcard_no = Column(String(20), comment="当前储值卡号")
    email = Column(String(60), comment="邮箱")
    image = Column(String(1000), comment="图片")
    organ_id = Column(String(65), comment="机构id（运营商）")
    phone = Column(String(60), comment="手机")
    address = Column(String(165), comment="用户地址")
    active = Column(Integer, comment="1、已激活 2、已注销 9、黑名单")
    sex = Column(String(10), comment="性别，根据字典数据来")
    birthday = Column(String(255), comment="生日")
    operator_id = Column(String(128), comment="商户号")
    open_id = Column(String(65), comment="小程序用户的openid")
    app_open_id = Column(String(65), comment="APP用户的openid")
    alipay_pid = Column(String(128), comment="支付宝账号pid")
    create_time = Column(DateTime, comment="创建日期")
    del_flag = Column(Integer, default=0, comment="删除标记 1:是 0:否")
    ali_open_id = Column(String(128))

    def __repr__(self):
        return f"<AppUser(id={self.id}, name='{self.name}', phone='{self.phone}')>"
