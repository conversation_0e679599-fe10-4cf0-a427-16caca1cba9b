"""
评分计算器模块
负责将各项指标标准化并计算综合评分
"""
from typing import Dict, List, Optional, Tuple, Any
import numpy as np
import logging
from enum import Enum

logger = logging.getLogger(__name__)


class ScoreGrade(str, Enum):
    """评分等级枚举"""
    S = "S"  # 90-100分
    A = "A"  # 80-89分
    B = "B"  # 70-79分
    C = "C"  # 60-69分
    D = "D"  # <60分


class ScoreCalculator:
    """评分计算器类"""
    
    # 评分维度权重配置
    DIMENSION_WEIGHTS = {
        "operation": 0.50,  # 运营效率 50%
        "service": 0.20,    # 服务质量 20%
        "stability": 0.30   # 稳定性 30%
    }
    
    # 运营效率指标权重
    OPERATION_WEIGHTS = {
        "daily_revenue_rate": 0.30,
        "device_utilization": 0.25,
        "revenue_per_device": 0.25,
        "completion_rate": 0.20
    }
    
    # 服务质量指标权重
    SERVICE_WEIGHTS = {
        "avg_charge_time": 0.25,
        "avg_charge_power": 0.30,
        "user_retention_rate": 0.30,
        "failure_rate": 0.15
    }
    
    # 稳定性指标权重
    STABILITY_WEIGHTS = {
        "order_growth_trend": 0.20,
        "revenue_stability": 0.25,
        "operation_continuity": 0.20,
        "device_health": 0.20,
        "time_balance": 0.15
    }
    
    def __init__(self):
        pass
    
    def percentile_normalize(
        self, 
        values: List[float], 
        higher_is_better: bool = True
    ) -> List[float]:
        """
        分位数标准化
        
        Args:
            values: 待标准化的数值列表
            higher_is_better: 是否数值越高越好
            
        Returns:
            标准化后的分数列表 (0-100分)
        """
        if not values:
            return []
        
        values = np.array(values)
        
        # 计算分位数
        percentiles = np.percentile(values, [10, 25, 50, 75, 90, 95])
        p10, p25, p50, p75, p90, p95 = percentiles
        
        def normalize_value(val):
            if higher_is_better:
                if val >= p95:
                    return 100
                elif val >= p90:
                    return 90 + (val - p90) / (p95 - p90) * 10
                elif val >= p75:
                    return 75 + (val - p75) / (p90 - p75) * 15
                elif val >= p50:
                    return 60 + (val - p50) / (p75 - p50) * 15
                elif val >= p25:
                    return 40 + (val - p25) / (p50 - p25) * 20
                elif val >= p10:
                    return 20 + (val - p10) / (p25 - p10) * 20
                else:
                    return max(0, 20 * val / p10) if p10 > 0 else 0
            else:
                # 数值越低越好的情况 (如故障率)
                if val <= p10:
                    return 100
                elif val <= p25:
                    return 90 + (p25 - val) / (p25 - p10) * 10
                elif val <= p50:
                    return 75 + (p50 - val) / (p50 - p25) * 15
                elif val <= p75:
                    return 60 + (p75 - val) / (p75 - p50) * 15
                elif val <= p90:
                    return 40 + (p90 - val) / (p90 - p75) * 20
                elif val <= p95:
                    return 20 + (p95 - val) / (p95 - p90) * 20
                else:
                    return max(0, 20 * (2 * p95 - val) / p95) if p95 > 0 else 0
        
        return [normalize_value(val) for val in values]
    
    def calculate_dimension_score(
        self, 
        metrics: Dict[str, float], 
        weights: Dict[str, float],
        normalization_data: Optional[Dict[str, List[float]]] = None
    ) -> float:
        """
        计算维度评分
        
        Args:
            metrics: 指标数值字典
            weights: 权重字典
            normalization_data: 标准化参考数据
            
        Returns:
            维度评分 (0-100分)
        """
        if not metrics:
            return 0.0
        
        total_score = 0.0
        total_weight = 0.0
        
        for metric_name, weight in weights.items():
            if metric_name in metrics:
                metric_value = metrics[metric_name]
                
                # 如果有标准化数据，进行标准化
                if normalization_data and metric_name in normalization_data:
                    ref_values = normalization_data[metric_name]
                    # 判断是否数值越高越好
                    higher_is_better = metric_name != "failure_rate"  # 故障率越低越好
                    normalized_scores = self.percentile_normalize(ref_values + [metric_value], higher_is_better)
                    metric_score = normalized_scores[-1]  # 取最后一个(当前值的标准化分数)
                else:
                    # 简单标准化：假设指标值已经是合理范围内的分数
                    metric_score = min(100, max(0, metric_value))
                
                total_score += metric_score * weight
                total_weight += weight
        
        return total_score / total_weight if total_weight > 0 else 0.0
    
    def calculate_total_score(
        self,
        operation_metrics: Dict[str, float],
        service_metrics: Dict[str, float],
        stability_metrics: Dict[str, float],
        normalization_data: Optional[Dict[str, Dict[str, List[float]]]] = None
    ) -> Dict[str, Any]:
        """
        计算综合评分
        
        Args:
            operation_metrics: 运营效率指标
            service_metrics: 服务质量指标
            stability_metrics: 稳定性指标
            normalization_data: 标准化参考数据
            
        Returns:
            包含各维度评分和总评分的字典
        """
        try:
            # 计算各维度评分
            operation_score = self.calculate_dimension_score(
                operation_metrics, 
                self.OPERATION_WEIGHTS,
                normalization_data.get("operation") if normalization_data else None
            )
            
            service_score = self.calculate_dimension_score(
                service_metrics, 
                self.SERVICE_WEIGHTS,
                normalization_data.get("service") if normalization_data else None
            )
            
            stability_score = self.calculate_dimension_score(
                stability_metrics, 
                self.STABILITY_WEIGHTS,
                normalization_data.get("stability") if normalization_data else None
            )
            
            # 计算综合评分
            total_score = (
                operation_score * self.DIMENSION_WEIGHTS["operation"] +
                service_score * self.DIMENSION_WEIGHTS["service"] +
                stability_score * self.DIMENSION_WEIGHTS["stability"]
            )
            
            # 确定评分等级
            grade = self.get_score_grade(total_score)
            
            return {
                "total_score": round(total_score, 2),
                "grade": grade,
                "dimension_scores": {
                    "operation_score": round(operation_score, 2),
                    "service_score": round(service_score, 2),
                    "stability_score": round(stability_score, 2)
                },
                "detailed_metrics": {
                    "operation": operation_metrics,
                    "service": service_metrics,
                    "stability": stability_metrics
                }
            }
            
        except Exception as e:
            logger.error(f"计算综合评分失败: {e}")
            raise
    
    def get_score_grade(self, score: float) -> str:
        """
        根据分数确定等级
        
        Args:
            score: 评分 (0-100)
            
        Returns:
            评分等级 (S/A/B/C/D)
        """
        if score >= 90:
            return ScoreGrade.S
        elif score >= 80:
            return ScoreGrade.A
        elif score >= 70:
            return ScoreGrade.B
        elif score >= 60:
            return ScoreGrade.C
        else:
            return ScoreGrade.D
    
    def batch_calculate_scores(
        self,
        stations_metrics: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """
        批量计算多个场站的评分
        
        Args:
            stations_metrics: 场站指标数据列表
            
        Returns:
            场站评分结果列表
        """
        if not stations_metrics:
            return []
        
        try:
            # 收集所有指标数据用于标准化
            normalization_data = self._collect_normalization_data(stations_metrics)
            
            # 计算每个场站的评分
            results = []
            for station_data in stations_metrics:
                station_id = station_data.get("station_id")
                operation_metrics = station_data.get("operation_metrics", {})
                service_metrics = station_data.get("service_metrics", {})
                stability_metrics = station_data.get("stability_metrics", {})
                
                score_result = self.calculate_total_score(
                    operation_metrics,
                    service_metrics,
                    stability_metrics,
                    normalization_data
                )
                
                score_result["station_id"] = station_id
                results.append(score_result)
            
            return results
            
        except Exception as e:
            logger.error(f"批量计算评分失败: {e}")
            raise
    
    def _collect_normalization_data(
        self, 
        stations_metrics: List[Dict[str, Any]]
    ) -> Dict[str, Dict[str, List[float]]]:
        """收集标准化参考数据"""
        normalization_data = {
            "operation": {},
            "service": {},
            "stability": {}
        }
        
        # 收集运营效率指标
        for metric_name in self.OPERATION_WEIGHTS.keys():
            values = []
            for station in stations_metrics:
                operation_metrics = station.get("operation_metrics", {})
                if metric_name in operation_metrics:
                    values.append(operation_metrics[metric_name])
            normalization_data["operation"][metric_name] = values
        
        # 收集服务质量指标
        for metric_name in self.SERVICE_WEIGHTS.keys():
            values = []
            for station in stations_metrics:
                service_metrics = station.get("service_metrics", {})
                if metric_name in service_metrics:
                    values.append(service_metrics[metric_name])
            normalization_data["service"][metric_name] = values
        
        # 收集稳定性指标
        for metric_name in self.STABILITY_WEIGHTS.keys():
            values = []
            for station in stations_metrics:
                stability_metrics = station.get("stability_metrics", {})
                if metric_name in stability_metrics:
                    values.append(stability_metrics[metric_name])
            normalization_data["stability"][metric_name] = values
        
        return normalization_data
