"""
场站相关的Pydantic模型
"""
from pydantic import BaseModel, Field
from typing import Optional
from datetime import datetime


class StationInfo(BaseModel):
    """场站基础信息"""
    station_id: str = Field(..., description="场站ID")
    station_name: str = Field(..., description="场站名称")
    city_name: str = Field(..., description="城市名称")
    address: Optional[str] = Field(None, description="地址")
    device_count: int = Field(..., description="设备数量")
    install_date: Optional[datetime] = Field(None, description="安装日期")
    run_date: Optional[datetime] = Field(None, description="投运日期")
    status: int = Field(..., description="状态")
    longitude: Optional[str] = Field(None, description="经度")
    latitude: Optional[str] = Field(None, description="纬度")

    class Config:
        from_attributes = True


class StationDetail(BaseModel):
    """场站详细信息"""
    basic_info: StationInfo
    current_score: Optional[dict] = None
    detailed_metrics: Optional[dict] = None
    basic_statistics: Optional[dict] = None
    recent_trends: Optional[list] = None

    class Config:
        from_attributes = True


class StationListRequest(BaseModel):
    """场站列表查询请求"""
    city_name: Optional[str] = Field(None, description="城市名称")
    grade: Optional[str] = Field(None, description="评分等级")
    min_score: Optional[float] = Field(None, description="最低分数")
    max_score: Optional[float] = Field(None, description="最高分数")
    page: int = Field(1, ge=1, description="页码")
    size: int = Field(20, ge=1, le=100, description="每页数量")
    sort: str = Field("total_score", description="排序字段")
    order: str = Field("desc", pattern="^(asc|desc)$", description="排序方向")


class StationTrendRequest(BaseModel):
    """场站趋势查询请求"""
    months: int = Field(12, ge=1, le=24, description="获取月数")
    dimension: str = Field("all", description="维度")
    include_details: bool = Field(False, description="是否包含详细指标趋势")


class StationCompareRequest(BaseModel):
    """场站对比分析请求"""
    base_period: Optional[str] = Field(None, description="基准期间")
    compare_period: str = Field(..., description="对比期间")
    compare_type: str = Field("month", description="对比类型")
