"""
评分相关的Pydantic模型
"""
from pydantic import BaseModel, Field
from typing import Optional, Dict, List, Any
from datetime import datetime


class MetricValue(BaseModel):
    """指标值模型"""
    value: float = Field(..., description="指标值")
    unit: str = Field(..., description="单位")
    description: str = Field(..., description="指标描述")
    weight: float = Field(..., description="权重")


class DimensionScores(BaseModel):
    """维度评分模型"""
    operation_efficiency: float = Field(..., description="运营效率评分")
    service_quality: float = Field(..., description="服务质量评分")
    stability: float = Field(..., description="稳定性评分")


class OperationMetrics(BaseModel):
    """运营效率指标"""
    daily_revenue_rate: MetricValue
    device_utilization: MetricValue
    revenue_per_device: MetricValue
    completion_rate: MetricValue


class ServiceMetrics(BaseModel):
    """服务质量指标"""
    avg_charge_time: MetricValue
    avg_charge_power: MetricValue
    user_retention_rate: MetricValue
    failure_rate: MetricValue


class StabilityMetrics(BaseModel):
    """稳定性指标"""
    order_growth_trend: MetricValue
    revenue_stability: MetricValue
    operation_continuity: MetricValue
    device_health_rate: MetricValue
    time_balance_index: MetricValue


class DetailedMetrics(BaseModel):
    """详细指标模型"""
    operation_efficiency: OperationMetrics
    service_quality: ServiceMetrics
    stability: StabilityMetrics


class BasicStatistics(BaseModel):
    """基础统计数据"""
    total_orders: int = Field(..., description="总订单数")
    completed_orders: int = Field(..., description="完成订单数")
    total_revenue: float = Field(..., description="总收益")
    operating_days: int = Field(..., description="运营天数")
    unique_users: int = Field(..., description="独立用户数")
    repeat_users: int = Field(..., description="复购用户数")


class TrendItem(BaseModel):
    """趋势项模型"""
    period: str = Field(..., description="统计周期")
    total_score: float = Field(..., description="总评分")
    score_change: Optional[float] = Field(None, description="评分变化")
    score_change_rate: Optional[float] = Field(None, description="评分变化率")
    city_rank: Optional[int] = Field(None, description="城市排名")
    rank_change: Optional[int] = Field(None, description="排名变化")


class StationScoreResponse(BaseModel):
    """场站评分响应模型"""
    station_id: str = Field(..., description="场站ID")
    station_name: str = Field(..., description="场站名称")
    city_name: str = Field(..., description="城市名称")
    total_score: float = Field(..., description="总评分")
    grade: str = Field(..., description="评分等级")
    city_rank: Optional[int] = Field(None, description="城市排名")
    national_rank: Optional[int] = Field(None, description="全国排名")
    scores: DimensionScores = Field(..., description="维度评分")
    basic_info: Dict[str, Any] = Field(..., description="基础信息")
    stat_period: str = Field(..., description="统计周期")
    update_time: datetime = Field(..., description="更新时间")

    class Config:
        from_attributes = True


class StationScoreDetailResponse(BaseModel):
    """场站评分详情响应模型"""
    station_id: str = Field(..., description="场站ID")
    stat_period: str = Field(..., description="统计周期")
    detailed_metrics: DetailedMetrics = Field(..., description="详细指标")
    basic_statistics: BasicStatistics = Field(..., description="基础统计")

    class Config:
        from_attributes = True


class StationScoreTrendResponse(BaseModel):
    """场站评分趋势响应模型"""
    station_id: str = Field(..., description="场站ID")
    station_name: str = Field(..., description="场站名称")
    trend_period: str = Field(..., description="趋势周期")
    trends: List[TrendItem] = Field(..., description="趋势数据")
    summary: Dict[str, Any] = Field(..., description="趋势摘要")

    class Config:
        from_attributes = True


class ScoreCalculationLogResponse(BaseModel):
    """评分计算日志响应模型"""
    batch_id: str = Field(..., description="批次ID")
    stat_period: str = Field(..., description="统计周期")
    task_type: str = Field(..., description="任务类型")
    trigger_type: str = Field(..., description="触发方式")
    total_stations: int = Field(..., description="总场站数")
    success_count: int = Field(..., description="成功计算数")
    failed_count: int = Field(..., description="失败计算数")
    status: str = Field(..., description="执行状态")
    progress_percentage: float = Field(..., description="进度百分比")
    start_time: Optional[datetime] = Field(None, description="开始时间")
    end_time: Optional[datetime] = Field(None, description="结束时间")
    duration_seconds: Optional[int] = Field(None, description="耗时(秒)")
    error_message: Optional[str] = Field(None, description="错误信息")

    class Config:
        from_attributes = True


class CalculationRequest(BaseModel):
    """计算请求模型"""
    period: Optional[str] = Field(None, description="计算周期")
    station_ids: Optional[List[str]] = Field(None, description="指定场站")
    force: bool = Field(False, description="是否强制重新计算")


class RankingRequest(BaseModel):
    """排行榜请求模型"""
    scope: str = Field(..., pattern="^(city|national)$", description="范围")
    city_name: Optional[str] = Field(None, description="城市名称")
    top: int = Field(10, ge=1, le=50, description="排行数量")
    period: Optional[str] = Field(None, description="统计周期")


class StatisticsRequest(BaseModel):
    """统计请求模型"""
    period: Optional[str] = Field(None, description="统计周期")
    city_name: Optional[str] = Field(None, description="城市筛选")
