"""
评分相关API路由
"""
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import Optional, List
from datetime import datetime

from app.database import get_db
from app.schemas.score import (
    CalculationRequest,
    RankingRequest, 
    StatisticsRequest,
    ScoreCalculationLogResponse
)
from app.services.score_service import ScoreService

router = APIRouter()


@router.get("/rankings", response_model=dict)
async def get_rankings(
    scope: str = Query(..., pattern="^(city|national)$", description="范围"),
    city_name: Optional[str] = Query(None, description="城市名称"),
    top: int = Query(10, ge=1, le=50, description="排行数量"),
    period: Optional[str] = Query(None, description="统计周期"),
    db: Session = Depends(get_db)
):
    """
    获取排行榜
    """
    try:
        score_service = ScoreService(db)
        rankings = await score_service.get_rankings(
            scope=scope,
            city_name=city_name,
            top=top,
            period=period
        )
        
        return {
            "code": 200,
            "message": "success",
            "data": rankings,
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取排行榜失败: {str(e)}")


@router.get("/statistics", response_model=dict)
async def get_statistics(
    period: Optional[str] = Query(None, description="统计周期"),
    city_name: Optional[str] = Query(None, description="城市筛选"),
    db: Session = Depends(get_db)
):
    """
    获取评分统计信息
    """
    try:
        score_service = ScoreService(db)
        statistics = await score_service.get_statistics(
            period=period,
            city_name=city_name
        )
        
        return {
            "code": 200,
            "message": "success",
            "data": statistics,
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取统计信息失败: {str(e)}")


@router.post("/calculate", response_model=dict)
async def trigger_calculation(
    request: CalculationRequest,
    db: Session = Depends(get_db)
):
    """
    触发评分计算任务
    """
    try:
        score_service = ScoreService(db)
        result = await score_service.trigger_calculation(
            period=request.period,
            station_ids=request.station_ids,
            force=request.force
        )
        
        return {
            "code": 200,
            "message": "计算任务已启动",
            "data": result,
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"启动计算任务失败: {str(e)}")


@router.get("/calculate/{batch_id}/status", response_model=dict)
async def get_calculation_status(
    batch_id: str,
    db: Session = Depends(get_db)
):
    """
    查询评分计算任务状态
    """
    try:
        score_service = ScoreService(db)
        status = await score_service.get_calculation_status(batch_id)
        
        if not status:
            raise HTTPException(status_code=404, detail="计算任务不存在")
        
        return {
            "code": 200,
            "message": "success",
            "data": status,
            "timestamp": datetime.now().isoformat()
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取计算状态失败: {str(e)}")


@router.get("/calculation-logs", response_model=dict)
async def get_calculation_logs(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页数量"),
    status: Optional[str] = Query(None, description="状态筛选"),
    period: Optional[str] = Query(None, description="周期筛选"),
    db: Session = Depends(get_db)
):
    """
    获取计算日志列表
    """
    try:
        score_service = ScoreService(db)
        logs = await score_service.get_calculation_logs(
            page=page,
            size=size,
            status=status,
            period=period
        )
        
        return {
            "code": 200,
            "message": "success",
            "data": logs,
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取计算日志失败: {str(e)}")
