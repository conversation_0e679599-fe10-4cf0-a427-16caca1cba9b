"""
场站相关API路由
"""
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import Optional
from datetime import datetime

from app.database import get_db
from app.schemas.station import (
    StationDetail, 
    StationListRequest, 
    StationTrendRequest, 
    StationCompareRequest
)
from app.schemas.score import StationScoreResponse
from app.services.station_service import StationService

router = APIRouter()


@router.get("/{station_id}/detail", response_model=dict)
async def get_station_detail(
    station_id: str,
    include_trends: bool = Query(True, description="是否包含趋势数据"),
    trend_months: int = Query(6, ge=1, le=24, description="趋势数据月数"),
    db: Session = Depends(get_db)
):
    """
    获取场站详细信息
    包含基础信息、当期评分、详细指标和近期趋势
    """
    try:
        station_service = StationService(db)
        detail = await station_service.get_station_detail(
            station_id, 
            include_trends=include_trends,
            trend_months=trend_months
        )
        
        if not detail:
            raise HTTPException(status_code=404, detail="场站不存在")
        
        return {
            "code": 200,
            "message": "success",
            "data": detail,
            "timestamp": datetime.now().isoformat()
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取场站详情失败: {str(e)}")


@router.get("/{station_id}/trends", response_model=dict)
async def get_station_trends(
    station_id: str,
    months: int = Query(12, ge=1, le=24, description="获取月数"),
    dimension: str = Query("all", description="维度"),
    include_details: bool = Query(False, description="是否包含详细指标趋势"),
    db: Session = Depends(get_db)
):
    """
    获取场站历史趋势数据
    """
    try:
        station_service = StationService(db)
        trends = await station_service.get_station_trends(
            station_id,
            months=months,
            dimension=dimension,
            include_details=include_details
        )
        
        if not trends:
            raise HTTPException(status_code=404, detail="场站趋势数据不存在")
        
        return {
            "code": 200,
            "message": "success",
            "data": trends,
            "timestamp": datetime.now().isoformat()
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取趋势数据失败: {str(e)}")


@router.get("/{station_id}/compare", response_model=dict)
async def get_station_compare(
    station_id: str,
    compare_period: str = Query(..., description="对比期间"),
    base_period: Optional[str] = Query(None, description="基准期间"),
    compare_type: str = Query("month", description="对比类型"),
    db: Session = Depends(get_db)
):
    """
    获取场站对比分析数据
    """
    try:
        station_service = StationService(db)
        comparison = await station_service.get_station_compare(
            station_id,
            compare_period=compare_period,
            base_period=base_period,
            compare_type=compare_type
        )
        
        if not comparison:
            raise HTTPException(status_code=404, detail="对比数据不存在")
        
        return {
            "code": 200,
            "message": "success",
            "data": comparison,
            "timestamp": datetime.now().isoformat()
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取对比数据失败: {str(e)}")


@router.get("/scores", response_model=dict)
async def get_station_scores(
    city_name: Optional[str] = Query(None, description="城市名称"),
    grade: Optional[str] = Query(None, description="评分等级"),
    min_score: Optional[float] = Query(None, description="最低分数"),
    max_score: Optional[float] = Query(None, description="最高分数"),
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页数量"),
    sort: str = Query("total_score", description="排序字段"),
    order: str = Query("desc", pattern="^(asc|desc)$", description="排序方向"),
    db: Session = Depends(get_db)
):
    """
    获取场站评分列表
    支持分页、筛选和排序
    """
    try:
        station_service = StationService(db)
        result = await station_service.get_station_scores(
            city_name=city_name,
            grade=grade,
            min_score=min_score,
            max_score=max_score,
            page=page,
            size=size,
            sort=sort,
            order=order
        )
        
        return {
            "code": 200,
            "message": "success",
            "data": result,
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取评分列表失败: {str(e)}")
