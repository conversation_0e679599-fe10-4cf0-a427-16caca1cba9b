"""
评分相关业务服务
"""
from sqlalchemy.orm import Session
from sqlalchemy import desc, asc, and_, or_, func
from typing import Optional, Dict, List, Any, Tuple
from datetime import datetime, timedelta
import logging
import uuid
import asyncio

from app.models.score import (
    StationScore,
    StationScoreDetail,
    StationScoreTrend,
    ScoreCalculationLog,
    CalculationStatus,
    TriggerType,
    TaskType
)
from app.models.station import PileStation
from app.core.analyzer import DataAnalyzer
from app.core.calculator import ScoreCalculator

logger = logging.getLogger(__name__)


class ScoreService:
    """评分服务类"""
    
    def __init__(self, db: Session):
        self.db = db
    
    async def get_rankings(
        self,
        scope: str,
        city_name: Optional[str] = None,
        top: int = 10,
        period: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        获取排行榜
        """
        try:
            # 构建查询
            query = self.db.query(StationScore)
            
            # 如果没有指定期间，使用最新期间
            if not period:
                latest_period_subquery = self.db.query(
                    func.max(StationScore.stat_period).label('max_period')
                ).subquery()
                
                query = query.filter(
                    StationScore.stat_period == latest_period_subquery.c.max_period
                )
                period = self.db.query(func.max(StationScore.stat_period)).scalar()
            else:
                query = query.filter(StationScore.stat_period == period)
            
            # 根据范围筛选
            if scope == "city":
                if not city_name:
                    raise ValueError("城市排行榜必须指定城市名称")
                query = query.filter(StationScore.city_name == city_name)
                query = query.order_by(asc(StationScore.city_rank))
            else:  # national
                query = query.order_by(asc(StationScore.national_rank))
            
            # 限制数量
            rankings = query.limit(top).all()
            
            # 获取统计信息
            total_stations = self.db.query(StationScore).filter(
                StationScore.stat_period == period
            ).count()
            
            if scope == "city" and city_name:
                total_stations = self.db.query(StationScore).filter(
                    StationScore.stat_period == period,
                    StationScore.city_name == city_name
                ).count()
            
            # 构建响应数据
            ranking_items = []
            for i, item in enumerate(rankings, 1):
                # 获取变化趋势
                trend = self.db.query(StationScoreTrend).filter(
                    StationScoreTrend.station_id == item.station_id,
                    StationScoreTrend.stat_period == period
                ).first()
                
                ranking_items.append({
                    "rank": i,
                    "station_id": item.station_id,
                    "station_name": item.station_name,
                    "total_score": float(item.total_score),
                    "grade": item.grade,
                    "score_change": float(trend.score_change) if trend and trend.score_change else None,
                    "rank_change": trend.city_rank_change if trend and scope == "city" else trend.national_rank_change if trend else None
                })
            
            return {
                "scope": scope,
                "city_name": city_name if scope == "city" else None,
                "period": period,
                "total_stations": total_stations,
                "rankings": ranking_items
            }
            
        except Exception as e:
            logger.error(f"获取排行榜失败: {e}")
            raise
    
    async def get_statistics(
        self,
        period: Optional[str] = None,
        city_name: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        获取评分统计信息
        """
        try:
            # 如果没有指定期间，使用最新期间
            if not period:
                period = self.db.query(func.max(StationScore.stat_period)).scalar()
            
            # 构建查询
            query = self.db.query(StationScore).filter(
                StationScore.stat_period == period
            )
            
            if city_name:
                query = query.filter(StationScore.city_name == city_name)
            
            scores = query.all()
            
            if not scores:
                return {
                    "period": period,
                    "total_stations": 0,
                    "grade_distribution": {},
                    "score_distribution": {},
                    "overall_metrics": {}
                }
            
            # 计算等级分布
            grade_counts = {}
            for score in scores:
                grade = score.grade
                if grade not in grade_counts:
                    grade_counts[grade] = 0
                grade_counts[grade] += 1
            
            total_count = len(scores)
            grade_distribution = {}
            for grade in ["S", "A", "B", "C", "D"]:
                count = grade_counts.get(grade, 0)
                grade_distribution[grade] = {
                    "count": count,
                    "percentage": round((count / total_count) * 100, 1) if total_count > 0 else 0
                }
            
            # 计算分数分布
            score_ranges = {
                "90-100": 0,
                "80-89": 0,
                "70-79": 0,
                "60-69": 0,
                "0-59": 0
            }
            
            for score in scores:
                score_val = float(score.total_score)
                if score_val >= 90:
                    score_ranges["90-100"] += 1
                elif score_val >= 80:
                    score_ranges["80-89"] += 1
                elif score_val >= 70:
                    score_ranges["70-79"] += 1
                elif score_val >= 60:
                    score_ranges["60-69"] += 1
                else:
                    score_ranges["0-59"] += 1
            
            # 计算城市统计
            city_stats = []
            if not city_name:  # 全国统计时显示各城市情况
                city_query = self.db.query(
                    StationScore.city_name,
                    func.count(StationScore.id).label('station_count'),
                    func.avg(StationScore.total_score).label('avg_score'),
                    func.sum(StationScore.total_revenue).label('total_revenue'),
                    func.count(func.case([(StationScore.grade.in_(["S", "A"]), 1)])).label('top_grade_count')
                ).filter(
                    StationScore.stat_period == period
                ).group_by(StationScore.city_name).all()
                
                for city_stat in city_query:
                    city_stats.append({
                        "city_name": city_stat.city_name,
                        "station_count": city_stat.station_count,
                        "avg_score": round(float(city_stat.avg_score), 1),
                        "top_grade_count": city_stat.top_grade_count,
                        "total_revenue": float(city_stat.total_revenue)
                    })
            
            # 计算整体指标
            score_values = [float(s.total_score) for s in scores]
            overall_metrics = {
                "avg_score": round(sum(score_values) / len(score_values), 1),
                "median_score": round(sorted(score_values)[len(score_values) // 2], 1),
                "std_score": round(self._calculate_std(score_values), 1),
                "total_revenue": sum([float(s.total_revenue) for s in scores]),
                "total_orders": sum([s.total_orders for s in scores])
            }
            
            return {
                "period": period,
                "total_stations": total_count,
                "grade_distribution": grade_distribution,
                "score_distribution": score_ranges,
                "city_statistics": city_stats,
                "overall_metrics": overall_metrics
            }
            
        except Exception as e:
            logger.error(f"获取统计信息失败: {e}")
            raise
    
    async def trigger_calculation(
        self,
        period: Optional[str] = None,
        station_ids: Optional[List[str]] = None,
        force: bool = False
    ) -> Dict[str, Any]:
        """
        触发评分计算任务
        """
        try:
            # 如果没有指定期间，使用上个月
            if not period:
                now = datetime.now()
                if now.day == 1:
                    # 如果是1号，计算上个月
                    last_month = now.replace(day=1) - timedelta(days=1)
                else:
                    # 否则计算当前月
                    last_month = now
                period = last_month.strftime("%Y-%m")
            
            # 生成批次ID
            batch_id = f"calc_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{str(uuid.uuid4())[:8]}"
            
            # 确定任务类型
            if station_ids:
                task_type = TaskType.SINGLE
                total_stations = len(station_ids)
            else:
                task_type = TaskType.FULL
                # 统计符合条件的场站数量
                total_stations = self.db.query(func.count(StationScore.station_id.distinct())).scalar() or 0
            
            # 检查是否已有计算任务在运行
            if not force:
                running_task = self.db.query(ScoreCalculationLog).filter(
                    ScoreCalculationLog.stat_period == period,
                    ScoreCalculationLog.status == CalculationStatus.RUNNING
                ).first()
                
                if running_task:
                    raise ValueError(f"期间 {period} 已有计算任务在运行: {running_task.batch_id}")
            
            # 创建计算日志
            calc_log = ScoreCalculationLog(
                batch_id=batch_id,
                stat_period=period,
                task_type=task_type,
                trigger_type=TriggerType.API,
                total_stations=total_stations,
                status=CalculationStatus.PENDING,
                start_time=datetime.now()
            )
            
            self.db.add(calc_log)
            self.db.commit()
            
            # 启动异步计算任务
            asyncio.create_task(self._execute_calculation(
                batch_id=batch_id,
                period=period,
                station_ids=station_ids
            ))

            return {
                "batch_id": batch_id,
                "period": period,
                "station_count": total_stations,
                "estimated_duration": total_stations * 2,  # 估算每个场站2秒
                "status": "PENDING"
            }
            
        except Exception as e:
            logger.error(f"触发计算任务失败: {e}")
            raise
    
    async def get_calculation_status(self, batch_id: str) -> Optional[Dict[str, Any]]:
        """
        获取计算任务状态
        """
        try:
            calc_log = self.db.query(ScoreCalculationLog).filter(
                ScoreCalculationLog.batch_id == batch_id
            ).first()
            
            if not calc_log:
                return None
            
            # 计算进度
            progress = {
                "total_stations": calc_log.total_stations,
                "completed": calc_log.success_count + calc_log.failed_count,
                "failed": calc_log.failed_count,
                "percentage": calc_log.progress_percentage
            }
            
            # 计算耗时
            timing = {
                "start_time": calc_log.start_time.isoformat() if calc_log.start_time else None,
                "end_time": calc_log.end_time.isoformat() if calc_log.end_time else None,
                "duration_seconds": calc_log.duration_seconds
            }
            
            # 结果信息
            result = {
                "success_count": calc_log.success_count,
                "failed_count": calc_log.failed_count,
                "error_message": calc_log.error_message
            }
            
            return {
                "batch_id": batch_id,
                "status": calc_log.status.value,
                "progress": progress,
                "timing": timing,
                "result": result
            }
            
        except Exception as e:
            logger.error(f"获取计算状态失败: {e}")
            raise
    
    async def get_calculation_logs(
        self,
        page: int = 1,
        size: int = 20,
        status: Optional[str] = None,
        period: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        获取计算日志列表
        """
        try:
            # 构建查询
            query = self.db.query(ScoreCalculationLog)
            
            if status:
                query = query.filter(ScoreCalculationLog.status == CalculationStatus(status))
            if period:
                query = query.filter(ScoreCalculationLog.stat_period == period)
            
            # 总数统计
            total = query.count()
            
            # 排序和分页
            query = query.order_by(desc(ScoreCalculationLog.create_time))
            offset = (page - 1) * size
            logs = query.offset(offset).limit(size).all()
            
            # 构建响应数据
            log_items = []
            for log in logs:
                log_items.append({
                    "batch_id": log.batch_id,
                    "stat_period": log.stat_period,
                    "task_type": log.task_type.value,
                    "trigger_type": log.trigger_type.value,
                    "total_stations": log.total_stations,
                    "success_count": log.success_count,
                    "failed_count": log.failed_count,
                    "status": log.status.value,
                    "progress_percentage": float(log.progress_percentage),
                    "start_time": log.start_time.isoformat() if log.start_time else None,
                    "end_time": log.end_time.isoformat() if log.end_time else None,
                    "duration_seconds": log.duration_seconds,
                    "error_message": log.error_message,
                    "create_time": log.create_time.isoformat()
                })
            
            return {
                "total": total,
                "page": page,
                "size": size,
                "pages": (total + size - 1) // size,
                "items": log_items
            }
            
        except Exception as e:
            logger.error(f"获取计算日志失败: {e}")
            raise
    
    def _calculate_std(self, values: List[float]) -> float:
        """计算标准差"""
        if len(values) < 2:
            return 0.0

        mean = sum(values) / len(values)
        variance = sum((x - mean) ** 2 for x in values) / (len(values) - 1)
        return variance ** 0.5

    async def _execute_calculation(
        self,
        batch_id: str,
        period: str,
        station_ids: Optional[List[str]] = None
    ):
        """
        执行评分计算任务
        """
        calc_log = None
        try:
            # 获取计算日志
            calc_log = self.db.query(ScoreCalculationLog).filter(
                ScoreCalculationLog.batch_id == batch_id
            ).first()

            if not calc_log:
                logger.error(f"计算日志不存在: {batch_id}")
                return

            # 更新状态为运行中
            calc_log.status = CalculationStatus.RUNNING
            calc_log.start_time = datetime.now()
            self.db.commit()

            # 获取需要计算的场站列表
            if station_ids:
                stations = self.db.query(PileStation).filter(
                    PileStation.id.in_(station_ids),
                    PileStation.status == 4  # 已投运
                ).all()
            else:
                stations = self.db.query(PileStation).filter(
                    PileStation.status == 4  # 已投运
                ).all()

            if not stations:
                calc_log.status = CalculationStatus.FAILED
                calc_log.error_message = "没有找到符合条件的场站"
                calc_log.end_time = datetime.now()
                self.db.commit()
                return

            # 解析期间
            start_date, end_date = self._parse_period(period)

            # 初始化分析器和计算器
            analyzer = DataAnalyzer(self.db)
            calculator = ScoreCalculator()

            # 收集所有场站的指标数据
            stations_metrics = []
            success_count = 0
            failed_count = 0

            for station in stations:
                try:
                    # 获取原始数据
                    raw_data = analyzer.get_station_raw_data(
                        station.id, start_date, end_date
                    )

                    # 计算各维度指标
                    operation_metrics = analyzer.calculate_operation_metrics(raw_data)
                    service_metrics = analyzer.calculate_service_metrics(raw_data)
                    stability_metrics = analyzer.calculate_stability_metrics(raw_data)

                    stations_metrics.append({
                        "station_id": station.id,
                        "station_name": station.name,
                        "city_name": station.city_name,
                        "operation_metrics": operation_metrics,
                        "service_metrics": service_metrics,
                        "stability_metrics": stability_metrics,
                        "raw_data": raw_data
                    })

                    success_count += 1

                except Exception as e:
                    logger.error(f"计算场站 {station.id} 指标失败: {e}")
                    failed_count += 1

                # 更新进度
                calc_log.success_count = success_count
                calc_log.failed_count = failed_count
                calc_log.progress_percentage = ((success_count + failed_count) / len(stations)) * 100
                self.db.commit()

            # 批量计算评分
            if stations_metrics:
                score_results = calculator.batch_calculate_scores(stations_metrics)

                # 保存评分结果
                await self._save_score_results(score_results, stations_metrics, period)

                # 计算排名
                await self._calculate_rankings(period)

            # 更新计算状态
            calc_log.status = CalculationStatus.SUCCESS
            calc_log.end_time = datetime.now()
            calc_log.duration_seconds = int((calc_log.end_time - calc_log.start_time).total_seconds())
            self.db.commit()

            logger.info(f"评分计算完成: {batch_id}, 成功: {success_count}, 失败: {failed_count}")

        except Exception as e:
            logger.error(f"评分计算任务失败: {batch_id}, 错误: {e}")
            if calc_log:
                calc_log.status = CalculationStatus.FAILED
                calc_log.error_message = str(e)
                calc_log.end_time = datetime.now()
                if calc_log.start_time:
                    calc_log.duration_seconds = int((calc_log.end_time - calc_log.start_time).total_seconds())
                self.db.commit()

    def _parse_period(self, period: str) -> Tuple[datetime, datetime]:
        """
        解析期间字符串，返回开始和结束日期

        Args:
            period: 期间字符串，格式为 YYYY-MM

        Returns:
            (开始日期, 结束日期)
        """
        try:
            year, month = map(int, period.split('-'))
            start_date = datetime(year, month, 1)

            # 计算月末日期
            if month == 12:
                end_date = datetime(year + 1, 1, 1) - timedelta(days=1)
            else:
                end_date = datetime(year, month + 1, 1) - timedelta(days=1)

            return start_date, end_date

        except Exception as e:
            logger.error(f"解析期间失败: {period}, 错误: {e}")
            raise ValueError(f"无效的期间格式: {period}")

    async def _save_score_results(
        self,
        score_results: List[Dict[str, Any]],
        stations_metrics: List[Dict[str, Any]],
        period: str
    ):
        """
        保存评分结果到数据库
        """
        try:
            # 创建站点指标映射
            metrics_map = {item["station_id"]: item for item in stations_metrics}

            for result in score_results:
                station_id = result["station_id"]
                station_data = metrics_map.get(station_id)

                if not station_data:
                    continue

                # 删除已存在的评分记录
                self.db.query(StationScore).filter(
                    StationScore.station_id == station_id,
                    StationScore.stat_period == period
                ).delete()

                self.db.query(StationScoreDetail).filter(
                    StationScoreDetail.station_id == station_id,
                    StationScoreDetail.stat_period == period
                ).delete()

                # 获取基础统计数据
                raw_data = station_data["raw_data"]
                orders = raw_data["orders"]
                devices = raw_data["devices"]
                period_info = raw_data["period"]

                # 创建主评分记录
                station_score = StationScore(
                    station_id=station_id,
                    station_name=station_data["station_name"],
                    city_name=station_data["city_name"],
                    total_score=result["total_score"],
                    grade=result["grade"],
                    operation_score=result["dimension_scores"]["operation_score"],
                    service_score=result["dimension_scores"]["service_score"],
                    stability_score=result["dimension_scores"]["stability_score"],
                    stat_period=period,
                    stat_start_date=period_info["start_date"].date(),
                    stat_end_date=period_info["end_date"].date(),
                    total_orders=len(orders),
                    completed_orders=len([o for o in orders if o.normal_end == 1]),
                    total_revenue=sum([float(o.total_fees or 0) for o in orders]),
                    device_count=len(devices),
                    operating_days=period_info["days"]
                )

                self.db.add(station_score)

                # 创建详细指标记录
                operation_metrics = station_data["operation_metrics"]
                service_metrics = station_data["service_metrics"]
                stability_metrics = station_data["stability_metrics"]

                # 计算用户相关指标
                user_ids = [o.user_id for o in orders if o.user_id]
                unique_users = len(set(user_ids))
                repeat_users = len(user_ids) - unique_users if unique_users > 0 else 0

                station_detail = StationScoreDetail(
                    station_id=station_id,
                    stat_period=period,
                    daily_revenue=operation_metrics.get("daily_revenue_rate", 0),
                    device_utilization=operation_metrics.get("device_utilization", 0),
                    completion_rate=operation_metrics.get("completion_rate", 0),
                    avg_charge_time=service_metrics.get("avg_charge_time", 0),
                    avg_charge_power=service_metrics.get("avg_charge_power", 0),
                    user_retention_rate=service_metrics.get("user_retention_rate", 0),
                    revenue_stability=stability_metrics.get("revenue_stability", 0),
                    operation_continuity=stability_metrics.get("operation_continuity", 0)
                )

                self.db.add(station_detail)

            self.db.commit()
            logger.info(f"保存 {len(score_results)} 个场站的评分结果")

        except Exception as e:
            logger.error(f"保存评分结果失败: {e}")
            self.db.rollback()
            raise

    async def _calculate_rankings(self, period: str):
        """
        计算排名
        """
        try:
            # 计算全国排名
            scores = self.db.query(StationScore).filter(
                StationScore.stat_period == period
            ).order_by(desc(StationScore.total_score)).all()

            for i, score in enumerate(scores, 1):
                score.national_rank = i

            # 计算城市排名
            cities = self.db.query(StationScore.city_name).filter(
                StationScore.stat_period == period
            ).distinct().all()

            for city_tuple in cities:
                city_name = city_tuple[0]
                city_scores = self.db.query(StationScore).filter(
                    StationScore.stat_period == period,
                    StationScore.city_name == city_name
                ).order_by(desc(StationScore.total_score)).all()

                for i, score in enumerate(city_scores, 1):
                    score.city_rank = i

            self.db.commit()
            logger.info(f"计算排名完成: {period}")

        except Exception as e:
            logger.error(f"计算排名失败: {e}")
            self.db.rollback()
            raise
