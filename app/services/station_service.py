"""
场站相关业务服务
"""
from sqlalchemy.orm import Session
from sqlalchemy import desc, asc, and_, or_, func
from typing import Optional, Dict, List, Any
from datetime import datetime, timedelta
import logging

from app.models.station import PileStation
from app.models.score import StationScore, StationScoreDetail, StationScoreTrend
from app.schemas.station import StationInfo

logger = logging.getLogger(__name__)


class StationService:
    """场站服务类"""
    
    def __init__(self, db: Session):
        self.db = db
    
    async def get_station_detail(
        self, 
        station_id: str, 
        include_trends: bool = True,
        trend_months: int = 6
    ) -> Optional[Dict[str, Any]]:
        """
        获取场站详细信息
        """
        try:
            # 获取场站基础信息
            station = self.db.query(PileStation).filter(
                PileStation.id == station_id,
                PileStation.del_flag == 0
            ).first()
            
            if not station:
                return None
            
            # 获取最新评分
            latest_score = self.db.query(StationScore).filter(
                StationScore.station_id == station_id
            ).order_by(desc(StationScore.stat_period)).first()
            
            # 获取详细指标
            score_detail = None
            if latest_score:
                score_detail = self.db.query(StationScoreDetail).filter(
                    StationScoreDetail.station_id == station_id,
                    StationScoreDetail.stat_period == latest_score.stat_period
                ).first()
            
            # 构建响应数据
            result = {
                "basic_info": {
                    "station_id": str(station.id),
                    "station_name": station.name,
                    "city_name": station.city_name,
                    "address": station.address,
                    "device_count": station.dc_ac_num or 0,
                    "install_date": station.install_date.isoformat() if station.install_date else None,
                    "run_date": station.run_date.isoformat() if station.run_date else None,
                    "status": station.status,
                    "longitude": station.longitude,
                    "latitude": station.latitude
                }
            }
            
            # 添加当期评分
            if latest_score:
                result["current_score"] = {
                    "stat_period": latest_score.stat_period,
                    "total_score": float(latest_score.total_score),
                    "grade": latest_score.grade,
                    "city_rank": latest_score.city_rank,
                    "national_rank": latest_score.national_rank,
                    "dimension_scores": {
                        "operation_efficiency": float(latest_score.operation_score),
                        "service_quality": float(latest_score.service_score),
                        "stability": float(latest_score.stability_score)
                    },
                    "update_time": latest_score.update_time.isoformat()
                }
                
                # 添加基础统计
                result["basic_statistics"] = {
                    "total_orders": latest_score.total_orders,
                    "completed_orders": latest_score.completed_orders,
                    "total_revenue": float(latest_score.total_revenue),
                    "operating_days": latest_score.operating_days,
                    "unique_users": latest_score.unique_users
                }
            
            # 添加详细指标
            if score_detail:
                result["detailed_metrics"] = self._build_detailed_metrics(score_detail)
            
            # 添加近期趋势
            if include_trends:
                trends = await self._get_recent_trends(station_id, trend_months)
                result["recent_trends"] = trends
            
            return result
            
        except Exception as e:
            logger.error(f"获取场站详情失败: {e}")
            raise
    
    async def get_station_trends(
        self,
        station_id: str,
        months: int = 12,
        dimension: str = "all",
        include_details: bool = False
    ) -> Optional[Dict[str, Any]]:
        """
        获取场站历史趋势数据
        """
        try:
            # 获取场站基础信息
            station = self.db.query(PileStation).filter(
                PileStation.id == station_id
            ).first()
            
            if not station:
                return None
            
            # 获取趋势数据
            trends_query = self.db.query(StationScoreTrend).filter(
                StationScoreTrend.station_id == station_id
            ).order_by(desc(StationScoreTrend.stat_period)).limit(months)
            
            trends = trends_query.all()
            
            if not trends:
                return None
            
            # 构建趋势数据
            trend_items = []
            for trend in reversed(trends):  # 按时间正序
                item = {
                    "period": trend.stat_period,
                    "total_score": float(trend.total_score),
                    "score_change": float(trend.score_change) if trend.score_change else None,
                    "score_change_rate": float(trend.score_change_rate) if trend.score_change_rate else None,
                    "city_rank": trend.city_rank,
                    "rank_change": trend.city_rank_change
                }
                
                if dimension == "all" or include_details:
                    item.update({
                        "operation_score_change": float(trend.operation_score_change) if trend.operation_score_change else None,
                        "service_score_change": float(trend.service_score_change) if trend.service_score_change else None,
                        "stability_score_change": float(trend.stability_score_change) if trend.stability_score_change else None
                    })
                
                trend_items.append(item)
            
            # 计算趋势摘要
            scores = [float(t.total_score) for t in trends]
            summary = {
                "avg_score": sum(scores) / len(scores),
                "max_score": max(scores),
                "min_score": min(scores),
                "score_trend": "上升" if trends[0].score_change and trends[0].score_change > 0 else "下降" if trends[0].score_change and trends[0].score_change < 0 else "稳定",
                "best_rank": min([t.city_rank for t in trends if t.city_rank]),
                "worst_rank": max([t.city_rank for t in trends if t.city_rank])
            }
            
            return {
                "station_id": station_id,
                "station_name": station.name,
                "trend_period": f"{trends[-1].stat_period} to {trends[0].stat_period}",
                "trends": trend_items,
                "summary": summary
            }
            
        except Exception as e:
            logger.error(f"获取趋势数据失败: {e}")
            raise
    
    async def get_station_compare(
        self,
        station_id: str,
        compare_period: str,
        base_period: Optional[str] = None,
        compare_type: str = "month"
    ) -> Optional[Dict[str, Any]]:
        """
        获取场站对比分析数据
        """
        try:
            # 如果没有指定基准期间，使用最新期间
            if not base_period:
                latest_score = self.db.query(StationScore).filter(
                    StationScore.station_id == station_id
                ).order_by(desc(StationScore.stat_period)).first()
                
                if not latest_score:
                    return None
                base_period = latest_score.stat_period
            
            # 获取两个期间的评分数据
            base_score = self.db.query(StationScore).filter(
                StationScore.station_id == station_id,
                StationScore.stat_period == base_period
            ).first()
            
            compare_score = self.db.query(StationScore).filter(
                StationScore.station_id == station_id,
                StationScore.stat_period == compare_period
            ).first()
            
            if not base_score or not compare_score:
                return None
            
            # 构建对比数据
            comparison = {
                "station_id": station_id,
                "station_name": base_score.station_name,
                "comparison": {
                    "base_period": base_period,
                    "compare_period": compare_period,
                    "compare_type": "环比" if compare_type == "month" else "同比"
                },
                "score_comparison": self._build_score_comparison(base_score, compare_score),
                "business_comparison": self._build_business_comparison(base_score, compare_score)
            }
            
            # 添加分析建议
            comparison["analysis"] = self._generate_analysis(base_score, compare_score)
            
            return comparison
            
        except Exception as e:
            logger.error(f"获取对比数据失败: {e}")
            raise
    
    async def get_station_scores(
        self,
        city_name: Optional[str] = None,
        grade: Optional[str] = None,
        min_score: Optional[float] = None,
        max_score: Optional[float] = None,
        page: int = 1,
        size: int = 20,
        sort: str = "total_score",
        order: str = "desc"
    ) -> Dict[str, Any]:
        """
        获取场站评分列表
        """
        try:
            # 构建查询
            query = self.db.query(StationScore)
            
            # 只查询最新期间的数据
            latest_period_subquery = self.db.query(
                StationScore.station_id,
                func.max(StationScore.stat_period).label('max_period')
            ).group_by(StationScore.station_id).subquery()
            
            query = query.join(
                latest_period_subquery,
                and_(
                    StationScore.station_id == latest_period_subquery.c.station_id,
                    StationScore.stat_period == latest_period_subquery.c.max_period
                )
            )
            
            # 添加筛选条件
            if city_name:
                query = query.filter(StationScore.city_name == city_name)
            if grade:
                query = query.filter(StationScore.grade == grade)
            if min_score is not None:
                query = query.filter(StationScore.total_score >= min_score)
            if max_score is not None:
                query = query.filter(StationScore.total_score <= max_score)
            
            # 总数统计
            total = query.count()
            
            # 排序
            sort_column = getattr(StationScore, sort, StationScore.total_score)
            if order == "desc":
                query = query.order_by(desc(sort_column))
            else:
                query = query.order_by(asc(sort_column))
            
            # 分页
            offset = (page - 1) * size
            items = query.offset(offset).limit(size).all()
            
            # 构建响应数据
            result_items = []
            for item in items:
                result_items.append({
                    "station_id": item.station_id,
                    "station_name": item.station_name,
                    "city_name": item.city_name,
                    "total_score": float(item.total_score),
                    "grade": item.grade,
                    "city_rank": item.city_rank,
                    "national_rank": item.national_rank,
                    "scores": {
                        "operation_score": float(item.operation_score),
                        "service_score": float(item.service_score),
                        "stability_score": float(item.stability_score)
                    },
                    "basic_info": {
                        "device_count": item.device_count,
                        "total_orders": item.total_orders,
                        "total_revenue": float(item.total_revenue),
                        "operating_days": item.operating_days
                    },
                    "stat_period": item.stat_period,
                    "update_time": item.update_time.isoformat()
                })
            
            return {
                "total": total,
                "page": page,
                "size": size,
                "pages": (total + size - 1) // size,
                "items": result_items
            }
            
        except Exception as e:
            logger.error(f"获取评分列表失败: {e}")
            raise
    
    def _build_detailed_metrics(self, detail: StationScoreDetail) -> Dict[str, Any]:
        """构建详细指标数据"""
        return {
            "operation_efficiency": {
                "daily_revenue_rate": {
                    "value": float(detail.daily_revenue_rate) if detail.daily_revenue_rate else 0,
                    "unit": "元/天",
                    "description": "日均收益率",
                    "weight": 30
                },
                "device_utilization": {
                    "value": float(detail.device_utilization) if detail.device_utilization else 0,
                    "unit": "%",
                    "description": "设备利用率",
                    "weight": 25
                },
                "revenue_per_device": {
                    "value": float(detail.revenue_per_device) if detail.revenue_per_device else 0,
                    "unit": "元/设备",
                    "description": "单设备产出",
                    "weight": 25
                },
                "completion_rate": {
                    "value": float(detail.completion_rate) if detail.completion_rate else 0,
                    "unit": "%",
                    "description": "充电完成率",
                    "weight": 20
                }
            },
            "service_quality": {
                "avg_charge_time": {
                    "value": float(detail.avg_charge_time) if detail.avg_charge_time else 0,
                    "unit": "小时",
                    "description": "平均充电时长",
                    "weight": 25
                },
                "avg_charge_power": {
                    "value": float(detail.avg_charge_power) if detail.avg_charge_power else 0,
                    "unit": "kWh",
                    "description": "平均充电量",
                    "weight": 30
                },
                "user_retention_rate": {
                    "value": float(detail.user_retention_rate) if detail.user_retention_rate else 0,
                    "unit": "%",
                    "description": "用户复购率",
                    "weight": 30
                },
                "failure_rate": {
                    "value": float(detail.failure_rate) if detail.failure_rate else 0,
                    "unit": "%",
                    "description": "故障率",
                    "weight": 15
                }
            },
            "stability": {
                "order_growth_trend": {
                    "value": float(detail.order_growth_trend) if detail.order_growth_trend else 0,
                    "unit": "%",
                    "description": "订单增长趋势",
                    "weight": 20
                },
                "revenue_stability": {
                    "value": float(detail.revenue_stability) if detail.revenue_stability else 0,
                    "unit": "%",
                    "description": "收益稳定性",
                    "weight": 25
                },
                "operation_continuity": {
                    "value": float(detail.operation_continuity) if detail.operation_continuity else 0,
                    "unit": "%",
                    "description": "运营连续性",
                    "weight": 20
                },
                "device_health_rate": {
                    "value": float(detail.device_health_rate) if detail.device_health_rate else 0,
                    "unit": "%",
                    "description": "设备健康度",
                    "weight": 20
                },
                "time_balance_index": {
                    "value": float(detail.time_balance_index) if detail.time_balance_index else 0,
                    "unit": "指数",
                    "description": "时段均衡性",
                    "weight": 15
                }
            }
        }
    
    async def _get_recent_trends(self, station_id: str, months: int) -> List[Dict[str, Any]]:
        """获取近期趋势数据"""
        trends = self.db.query(StationScoreTrend).filter(
            StationScoreTrend.station_id == station_id
        ).order_by(desc(StationScoreTrend.stat_period)).limit(months).all()
        
        result = []
        for trend in reversed(trends):  # 按时间正序
            result.append({
                "period": trend.stat_period,
                "total_score": float(trend.total_score),
                "score_change": float(trend.score_change) if trend.score_change else None,
                "score_change_rate": float(trend.score_change_rate) if trend.score_change_rate else None,
                "city_rank": trend.city_rank,
                "rank_change": trend.city_rank_change
            })
        
        return result
    
    def _build_score_comparison(self, base: StationScore, compare: StationScore) -> Dict[str, Any]:
        """构建评分对比数据"""
        def calc_change(base_val, compare_val):
            if base_val and compare_val:
                change = float(base_val) - float(compare_val)
                change_rate = (change / float(compare_val)) * 100
                trend = "上升" if change > 0 else "下降" if change < 0 else "持平"
                return {
                    "base": float(base_val),
                    "compare": float(compare_val),
                    "change": round(change, 2),
                    "change_rate": round(change_rate, 2),
                    "trend": trend
                }
            return None
        
        return {
            "total_score": calc_change(base.total_score, compare.total_score),
            "operation_efficiency": calc_change(base.operation_score, compare.operation_score),
            "service_quality": calc_change(base.service_score, compare.service_score),
            "stability": calc_change(base.stability_score, compare.stability_score)
        }
    
    def _build_business_comparison(self, base: StationScore, compare: StationScore) -> Dict[str, Any]:
        """构建业务对比数据"""
        def calc_change(base_val, compare_val):
            if base_val is not None and compare_val is not None:
                change = base_val - compare_val
                change_rate = (change / compare_val) * 100 if compare_val != 0 else 0
                trend = "上升" if change > 0 else "下降" if change < 0 else "持平"
                return {
                    "base": base_val,
                    "compare": compare_val,
                    "change": change,
                    "change_rate": round(change_rate, 2),
                    "trend": trend
                }
            return None
        
        return {
            "total_orders": calc_change(base.total_orders, compare.total_orders),
            "total_revenue": calc_change(float(base.total_revenue), float(compare.total_revenue))
        }
    
    def _generate_analysis(self, base: StationScore, compare: StationScore) -> Dict[str, Any]:
        """生成分析建议"""
        improvement_areas = []
        concern_areas = []
        recommendations = []
        
        # 简单的分析逻辑
        if base.operation_score > compare.operation_score:
            improvement_areas.append("运营效率有所提升")
        elif base.operation_score < compare.operation_score:
            concern_areas.append("运营效率有所下降")
        
        if base.service_score > compare.service_score:
            improvement_areas.append("服务质量有所提升")
        elif base.service_score < compare.service_score:
            concern_areas.append("服务质量有所下降")
        
        if base.stability_score > compare.stability_score:
            improvement_areas.append("稳定性有所提升")
        elif base.stability_score < compare.stability_score:
            concern_areas.append("稳定性有所下降")
        
        # 生成建议
        if base.total_score < compare.total_score:
            recommendations.append("建议分析各项指标变化原因，制定改进措施")
        
        return {
            "improvement_areas": improvement_areas,
            "concern_areas": concern_areas,
            "recommendations": recommendations
        }
