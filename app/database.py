"""
数据库连接和会话管理
"""
from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session
from contextlib import contextmanager
from typing import Generator
import logging

from app.config import settings

logger = logging.getLogger(__name__)

# 创建读数据库引擎（正式数据库，只读）
read_engine = create_engine(
    settings.read_database_url,
    echo=settings.database_echo,
    pool_pre_ping=True,
    pool_recycle=3600,
    pool_size=10,
    max_overflow=20
)

# 创建写数据库引擎（测试数据库，读写）
write_engine = create_engine(
    settings.write_database_url,
    echo=settings.database_echo,
    pool_pre_ping=True,
    pool_recycle=3600,
    pool_size=10,
    max_overflow=20
)

# 创建会话工厂
ReadSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=read_engine)
WriteSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=write_engine)

# 默认使用写库（向后兼容）
SessionLocal = WriteSessionLocal
engine = write_engine

# 创建基础模型类
Base = declarative_base()


def get_db() -> Generator[Session, None, None]:
    """
    获取数据库会话（默认写库）
    用于FastAPI依赖注入
    """
    db = SessionLocal()
    try:
        yield db
    except Exception as e:
        logger.error(f"数据库会话异常: {e}")
        db.rollback()
        raise
    finally:
        db.close()


def get_read_db() -> Generator[Session, None, None]:
    """
    获取读数据库会话（正式数据库，只读）
    用于读取原始数据
    """
    db = ReadSessionLocal()
    try:
        yield db
    except Exception as e:
        logger.error(f"读数据库会话异常: {e}")
        db.rollback()
        raise
    finally:
        db.close()


def get_write_db() -> Generator[Session, None, None]:
    """
    获取写数据库会话（测试数据库，读写）
    用于写入评分结果
    """
    db = WriteSessionLocal()
    try:
        yield db
    except Exception as e:
        logger.error(f"写数据库会话异常: {e}")
        db.rollback()
        raise
    finally:
        db.close()



@contextmanager
def get_db_session() -> Generator[Session, None, None]:
    """
    获取数据库会话上下文管理器
    用于业务逻辑中的数据库操作
    """
    db = SessionLocal()
    try:
        yield db
        db.commit()
    except Exception as e:
        logger.error(f"数据库事务异常: {e}")
        db.rollback()
        raise
    finally:
        db.close()


def init_database():
    """初始化数据库表（只创建新的评分表）"""
    try:
        # 导入评分相关模型
        from app.models.score import StationScore, StationScoreDetail, StationScoreTrend, ScoreCalculationLog

        # 只在写库中创建评分相关的表
        # station和order表已存在于读库中，不需要创建
        Base.metadata.create_all(bind=write_engine, tables=[
            StationScore.__table__,
            StationScoreDetail.__table__,
            StationScoreTrend.__table__,
            ScoreCalculationLog.__table__
        ])
        logger.info("评分数据库表初始化完成")
    except Exception as e:
        logger.error(f"数据库初始化失败: {e}")
        raise


def check_database_connection() -> bool:
    """检查数据库连接"""
    try:
        # 检查读库连接
        with read_engine.connect() as conn:
            conn.execute("SELECT 1")
        logger.info("读数据库连接正常")

        # 检查写库连接
        with write_engine.connect() as conn:
            conn.execute("SELECT 1")
        logger.info("写数据库连接正常")

        return True
    except Exception as e:
        logger.error(f"数据库连接失败: {e}")
        return False
