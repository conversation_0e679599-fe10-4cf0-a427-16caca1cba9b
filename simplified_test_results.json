{"timestamp": "2025-08-12T17:11:57.414850", "simplification_approach": {"method": "基于当前设备数量和调运记录做加减法", "formula": "平均设备数 = 当前设备数 + (调入数 - 调出数) * 0.5", "assumption": "调运在统计期中间发生，影响一半的时间", "advantages": ["计算简单高效", "适合现实中不频繁的调运情况", "易于理解和维护", "性能优秀"]}, "test_results": {"average_device_count": 5.32258064516129, "complete_workflow": {"operation_metrics": {"daily_revenue_rate": 238.06, "device_utilization": 0.5455, "revenue_per_device": 1386.55, "completion_rate": 0.0}, "service_metrics": {"avg_charge_time": 10377.78, "avg_charge_power": 70.0, "user_retention_rate": 100.0, "failure_rate": 0.0}, "stability_metrics": {"order_growth_trend": 0.0, "revenue_stability": 78.98, "operation_continuity": 96.77, "device_health": 100.0, "time_balance": 88.45}, "score_result": {"total_score": 64.48, "grade": "C", "dimension_scores": {"operation_score": 55.14, "service_score": 76.0, "stability_score": 72.37}, "detailed_metrics": {"operation": {"daily_revenue_rate": 238.06, "device_utilization": 0.5455, "revenue_per_device": 1386.55, "completion_rate": 0.0}, "service": {"avg_charge_time": 10377.78, "avg_charge_power": 70.0, "user_retention_rate": 100.0, "failure_rate": 0.0}, "stability": {"order_growth_trend": 0.0, "revenue_stability": 78.98, "operation_continuity": 96.77, "device_health": 100.0, "time_balance": 88.45}}}, "simplification_summary": {"current_devices": 5, "devices_in": 1, "devices_out": 1, "net_change": 0, "average_devices": 5}}}}