# 充电场站评分系统

基于Python FastAPI开发的充电场站评分评估系统，通过分析历史订单数据对充电场站进行综合评分。

## 功能特性

- **13个核心指标评分**：运营效率、服务质量、稳定性三大维度
- **历史趋势分析**：支持月度评分变化趋势查询
- **排行榜功能**：城市和全国排行榜
- **对比分析**：支持不同时期的评分对比
- **RESTful API**：标准化的API接口设计
- **自动化计算**：支持定时任务和手动触发

## 技术栈

- **后端框架**: FastAPI
- **数据库**: MySQL
- **ORM**: SQLAlchemy
- **任务调度**: APScheduler
- **数据处理**: Pandas + NumPy
- **部署**: Docker

## 快速开始

### 1. 环境准备

```bash
# 克隆项目
git clone <repository-url>
cd station_evaluation

# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate  # Windows

# 安装依赖
pip install -r requirements.txt
```

### 2. 配置环境

```bash
# 复制环境配置文件
cp .env.example .env

# 编辑配置文件
vim .env
```

主要配置项：
- `DATABASE_URL`: 数据库连接字符串
- `DEBUG`: 是否开启调试模式
- 各项评分权重配置

### 3. 数据库初始化

```bash
# 确保MySQL服务运行
# 创建数据库
mysql -u root -p -e "CREATE DATABASE IF NOT EXISTS \`chargepile-v3.0\` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"

# 导入基础表结构
mysql -u root -p chargepile-v3.0 < sql_base/pile_station.sql
mysql -u root -p chargepile-v3.0 < sql_base/pile_device.sql
mysql -u root -p chargepile-v3.0 < sql_base/pile_order_snapshot.sql
mysql -u root -p chargepile-v3.0 < sql_base/app_user.sql
```

### 4. 启动应用

```bash
# 开发模式启动
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

# 或使用Python直接运行
python -m app.main
```

### 5. Docker部署

```bash
# 使用docker-compose启动
docker-compose up -d

# 查看日志
docker-compose logs -f app
```

## API文档

启动应用后访问：
- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc

### 核心接口

#### 场站详情
```
GET /api/v1/stations/{station_id}/detail
```

#### 历史趋势
```
GET /api/v1/stations/{station_id}/trends?months=12
```

#### 对比分析
```
GET /api/v1/stations/{station_id}/compare?compare_period=2024-12
```

#### 评分列表
```
GET /api/v1/stations/scores?city_name=北京市&page=1&size=20
```

#### 排行榜
```
GET /api/v1/scores/rankings?scope=city&city_name=北京市&top=10
```

#### 统计信息
```
GET /api/v1/scores/statistics?period=2025-01
```

## 评分体系

### 运营效率指标 (50%权重)
- 日均收益率 (30%)
- 设备利用率 (25%)
- 单设备产出 (25%)
- 充电完成率 (20%)

### 服务质量指标 (30%权重)
- 平均充电时长 (25%)
- 平均充电量 (30%)
- 用户复购率 (30%)
- 故障率 (15%)

### 稳定性指标 (20%权重)
- 订单增长趋势 (20%)
- 收益稳定性 (25%)
- 运营连续性 (20%)
- 设备健康度 (20%)
- 时段均衡性 (15%)

## 项目结构

```
station_evaluation/
├── app/
│   ├── __init__.py
│   ├── main.py                 # FastAPI应用入口
│   ├── config.py               # 配置文件
│   ├── database.py             # 数据库连接
│   ├── models/                 # 数据模型
│   │   ├── station.py          # 场站模型
│   │   ├── order.py            # 订单模型
│   │   └── score.py            # 评分模型
│   ├── schemas/                # Pydantic模型
│   ├── api/                    # API路由
│   │   └── v1/
│   │       ├── stations.py     # 场站API
│   │       └── scores.py       # 评分API
│   ├── services/               # 业务服务
│   │   ├── station_service.py  # 场站服务
│   │   └── score_service.py    # 评分服务
│   └── core/                   # 核心业务逻辑
├── sql_base/                   # 数据库结构文件
├── requirements.txt            # 依赖包
├── Dockerfile                  # Docker配置
├── docker-compose.yml          # Docker编排
└── README.md                   # 项目说明
```

## 开发计划

- [x] **Day 1-2**: 项目初始化和数据模型设计
- [ ] **Day 3-4**: 评分算法核心实现
- [ ] **Day 5-6**: API开发和数据存储

## 许可证

MIT License
