# 配置文件说明

## config.py vs .env.example 的区别

### 1. **config.py** - 应用配置类
- **作用**: 定义配置项的结构、默认值、类型验证
- **内容**: Python类，包含所有配置项的定义和业务逻辑
- **特点**: 
  - 使用Pydantic进行类型验证
  - 包含配置项的默认值
  - 包含业务相关的常量和映射
  - 代码中直接导入使用

### 2. **.env.example** - 环境变量模板
- **作用**: 提供环境变量的示例和说明
- **内容**: 键值对格式，展示需要配置的环境变量
- **特点**:
  - 不包含敏感信息（如真实密码）
  - 作为.env文件的模板
  - 便于部署时快速配置

### 3. **实际使用流程**
```bash
# 1. 复制模板文件
cp .env.example .env

# 2. 编辑.env文件，填入真实配置
vim .env

# 3. 应用启动时，config.py会自动读取.env文件
```

### 4. **配置优先级**
1. 环境变量 (.env文件)
2. config.py中的默认值
3. Pydantic的类型默认值

### 5. **示例对比**

**config.py**:
```python
class Settings(BaseSettings):
    database_url: str = "mysql+pymysql://user:password@localhost:3306/db"
    debug: bool = False
    
    class Config:
        env_file = ".env"  # 自动读取.env文件
```

**.env.example**:
```bash
# 数据库配置
DATABASE_URL=mysql+pymysql://user:password@localhost:3306/chargepile-v3.0
DEBUG=False
```

**实际的.env文件**:
```bash
# 数据库配置
DATABASE_URL=mysql+pymysql://root:mypassword@*************:3306/chargepile-v3.0
DEBUG=True
```
