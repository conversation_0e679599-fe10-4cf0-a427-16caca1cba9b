# 充电场站评分系统设计方案与开发文档

## 一、项目概述

### 1.1 项目目标
开发一个基于Python的充电场站评分评估系统，通过分析历史订单数据，对充电场站进行综合评分，为运营决策提供数据支持。

### 1.2 技术栈
- **后端框架**: FastAPI
- **数据库**: MySQL 
- **ORM**: SQLAlchemy
- **任务调度**: APScheduler
- **数据处理**: Pandas + NumPy
- **部署**: Docker

### 1.3 开发周期
6个工作日完成MVP版本

## 二、完整评分模型

### 2.1 评分维度 (13个核心指标)

**A. 运营效率指标 (50%权重)**
- 日均收益率: `total_fees / 运营天数` (30%权重)
- 设备利用率: `充电订单数 / (设备数量 × 运营天数)` (25%权重)
- 单设备产出: `total_fees / dc_ac_num` (25%权重)
- 充电完成率: `完成订单数 / 总订单数` (20%权重)

**B. 稳定性指标 (30%权重)**
- 订单增长趋势: `(近期订单-历史订单)/历史订单×100` (20%权重)
- 收益稳定性: `1 - (收益标准差 / 收益均值)` (25%权重)
- 运营连续性: `连续运营天数 / 总天数` (20%权重)
- 设备健康度: `正常运行设备数 / 总设备数` (20%权重)
- 时段均衡性: `基于不同时段利用率方差计算` (15%权重)

**C. 服务质量指标 (20%权重)**
- 平均充电时长: `AVG(timelong)` (25%权重)
- 平均充电量: `AVG(charge_power)` (30%权重)
- 用户复购率: `重复用户数 / 总用户数` (30%权重)
- 故障率: `异常结束订单数 / 总订单数` (15%权重)



### 2.2 评分算法
```python
# 标准化公式 (分位数标准化)
def percentile_normalize(values):
    percentiles = np.percentile(values, [25, 50, 75, 90, 95])
    # 基于分位数映射到0-100分

# 维度内权重计算
operation_score = (
    daily_revenue_rate * 0.30 + device_utilization * 0.25 +
    revenue_per_device * 0.25 + completion_rate * 0.20
)

# 综合评分
total_score = (operation_score * 0.5) + (service_score * 0.2) + (stability_score * 0.3)

# 等级划分
S级: 90-100分, A级: 80-89分, B级: 70-79分, C级: 60-69分, D级: <60分
```

## 三、数据存储设计

### 3.1 新增数据表

#### 3.1.1 场站评分表 (station_scores)
```sql
CREATE TABLE station_scores (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    station_id VARCHAR(64) NOT NULL COMMENT '场站ID',
    station_name VARCHAR(200) COMMENT '场站名称',
    city_name VARCHAR(300) COMMENT '城市名称',
    
    -- 评分数据
    total_score DECIMAL(5,2) COMMENT '总评分',
    grade CHAR(1) COMMENT '评分等级 S/A/B/C/D',
    
    -- 分维度评分
    operation_score DECIMAL(5,2) COMMENT '运营效率评分',
    service_score DECIMAL(5,2) COMMENT '服务质量评分',
    stability_score DECIMAL(5,2) COMMENT '稳定性评分',
    
    -- 排名信息
    city_rank INT COMMENT '城市排名',
    national_rank INT COMMENT '全国排名',
    
    -- 统计周期
    stat_period VARCHAR(20) COMMENT '统计周期 YYYY-MM',
    stat_start_date DATE COMMENT '统计开始日期',
    stat_end_date DATE COMMENT '统计结束日期',
    
    -- 基础统计
    total_orders INT COMMENT '总订单数',
    completed_orders INT COMMENT '完成订单数',
    total_revenue DECIMAL(10,2) COMMENT '总收益',
    device_count INT COMMENT '设备数量',
    operating_days INT COMMENT '运营天数',
    
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    UNIQUE KEY uk_station_period (station_id, stat_period),
    INDEX idx_city_score (city_name, total_score),
    INDEX idx_grade (grade),
    INDEX idx_period (stat_period)
) COMMENT='场站评分表';
```

#### 3.1.2 评分详细指标表 (station_score_details)
```sql
CREATE TABLE station_score_details (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    station_id VARCHAR(64) NOT NULL,
    stat_period VARCHAR(20) NOT NULL,
    
    -- 运营效率指标
    daily_revenue DECIMAL(8,2) COMMENT '日均收益',
    device_utilization DECIMAL(5,2) COMMENT '设备利用率',
    completion_rate DECIMAL(5,2) COMMENT '充电完成率',
    
    -- 服务质量指标
    avg_charge_time DECIMAL(6,2) COMMENT '平均充电时长(小时)',
    avg_charge_power DECIMAL(8,4) COMMENT '平均充电量(kWh)',
    user_retention_rate DECIMAL(5,2) COMMENT '用户复购率',
    
    -- 稳定性指标
    revenue_stability DECIMAL(5,2) COMMENT '收益稳定性',
    operation_continuity DECIMAL(5,2) COMMENT '运营连续性',
    
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE KEY uk_station_period_detail (station_id, stat_period),
    INDEX idx_period_detail (stat_period)
) COMMENT='场站评分详细指标表';
```

#### 3.1.3 评分计算日志表 (score_calculation_logs)
```sql
CREATE TABLE score_calculation_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    batch_id VARCHAR(64) NOT NULL COMMENT '批次ID',
    stat_period VARCHAR(20) NOT NULL,
    
    total_stations INT COMMENT '总场站数',
    success_count INT COMMENT '成功计算数',
    failed_count INT COMMENT '失败计算数',
    
    start_time TIMESTAMP COMMENT '开始时间',
    end_time TIMESTAMP COMMENT '结束时间',
    duration_seconds INT COMMENT '耗时(秒)',
    
    status ENUM('RUNNING', 'SUCCESS', 'FAILED') COMMENT '状态',
    error_message TEXT COMMENT '错误信息',
    
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) COMMENT='评分计算日志表';
```

### 3.2 Redis缓存设计

#### 3.2.1 缓存作用说明
Redis在系统中的主要作用：
- **性能加速**: 缓存热点查询数据，提升API响应速度
- **减轻数据库压力**: 避免重复的复杂查询和聚合计算
- **实时状态管理**: 缓存计算任务进度，支持实时查询
- **临时存储**: 非持久化存储，数据最终保存在MySQL中

```python
# 缓存键设计
CACHE_KEYS = {
    "station_score": "station:score:{station_id}:{period}",  # 场站评分
    "city_ranking": "city:ranking:{city_name}:{period}",     # 城市排行
    "national_ranking": "national:ranking:{period}",         # 全国排行
    "score_stats": "score:stats:{period}",                   # 评分统计
    "calculation_status": "calc:status:{batch_id}"           # 计算状态
}

# 缓存过期时间
CACHE_TTL = {
    "station_score": 3600,      # 1小时 - 单个场站评分
    "ranking": 1800,            # 30分钟 - 排行榜数据
    "stats": 7200,              # 2小时 - 统计数据
    "status": 300               # 5分钟 - 计算状态
}

# 缓存更新策略
CACHE_UPDATE_STRATEGY = {
    "计算完成后": "立即更新相关缓存",
    "API查询时": "缓存未命中时查询数据库并缓存",
    "定时清理": "每日凌晨清理过期缓存",
    "手动刷新": "支持管理员手动清理缓存"
}
```

#### 3.2.2 数据持久化存储（简化架构）
```python
# MySQL作为唯一的数据存储，去除Redis
SIMPLIFIED_STORAGE = {
    "station_scores": "场站月度评分主表",
    "station_score_details": "13个详细指标数据",
    "station_score_trends": "历史变化趋势数据",
    "score_calculation_logs": "计算任务日志"
}

# 性能优化策略
PERFORMANCE_OPTIMIZATION = {
    "索引优化": "合理设计复合索引",
    "视图优化": "创建常用查询视图",
    "分区表": "按时间分区提升查询性能",
    "查询缓存": "MySQL查询缓存机制"
}

# 前端交互优化
FRONTEND_OPTIMIZATION = {
    "一体化API": "一次调用获取完整数据",
    "标准化格式": "统一的JSON响应格式",
    "懒加载": "按需加载历史趋势数据",
    "前端缓存": "浏览器端缓存5分钟"
}
```

#### 3.2.3 首次计算数据范围
```python
# 首次计算策略
INITIAL_CALCULATION = {
    "数据范围": "近6个月完整数据",
    "最小要求": "至少3个月数据才进行评分",
    "场站筛选": "运营天数≥30天的场站",
    "订单筛选": "充电完成订单≥50笔的场站"
}

# 计算示例
def get_initial_calculation_period():
    """获取首次计算的数据范围"""
    end_date = datetime.now().replace(day=1) - timedelta(days=1)  # 上月最后一天
    start_date = end_date - timedelta(days=180)  # 往前6个月

    return {
        "start_date": start_date.strftime("%Y-%m-%d"),
        "end_date": end_date.strftime("%Y-%m-%d"),
        "periods": generate_monthly_periods(start_date, end_date)
    }

# 数据质量要求
DATA_QUALITY_REQUIREMENTS = {
    "场站要求": {
        "运营状态": "status = 4 (已投运)",
        "运营时长": "≥30天",
        "设备数量": "≥1台充电车"
    },
    "订单要求": {
        "订单状态": "status = 4 (充电完成)",
        "订单数量": "≥50笔/月",
        "数据完整性": "必须包含费用、时长、电量数据"
    }
}
```

## 四、项目结构

```
station_evaluation/
├── app/
│   ├── __init__.py
│   ├── main.py                 # FastAPI应用入口
│   ├── config.py               # 配置文件
│   ├── database.py             # 数据库连接
│   ├── models/                 # 数据模型
│   │   ├── __init__.py
│   │   ├── station.py          # 场站模型
│   │   ├── order.py            # 订单模型
│   │   └── score.py            # 评分模型
│   ├── schemas/                # Pydantic模型
│   │   ├── __init__.py
│   │   ├── station.py
│   │   └── score.py
│   ├── api/                    # API路由
│   │   ├── __init__.py
│   │   ├── v1/
│   │   │   ├── __init__.py
│   │   │   ├── stations.py     # 场站相关API
│   │   │   └── scores.py       # 评分相关API
│   ├── core/                   # 核心业务逻辑
│   │   ├── __init__.py
│   │   ├── calculator.py       # 评分计算器
│   │   ├── analyzer.py         # 数据分析器
│   │   └── cache.py            # 缓存管理
│   ├── services/               # 业务服务
│   │   ├── __init__.py
│   │   ├── score_service.py    # 评分服务
│   │   └── station_service.py  # 场站服务
│   └── tasks/                  # 定时任务
│       ├── __init__.py
│       └── score_tasks.py      # 评分计算任务
├── tests/                      # 测试文件
├── requirements.txt            # 依赖包
├── Dockerfile                  # Docker配置
├── docker-compose.yml          # Docker编排
└── README.md                   # 项目说明
```

## 五、核心API设计

### 5.1 API路径规划（优化版）
```
/api/v1/stations/{station_id}/detail       # 获取场站完整详情（一体化接口）
/api/v1/stations/{station_id}/trends       # 获取历史趋势数据
/api/v1/stations/{station_id}/compare      # 获取对比分析数据
/api/v1/stations/scores                    # 获取场站评分列表
/api/v1/scores/rankings                    # 获取排行榜
/api/v1/scores/statistics                  # 获取评分统计
/api/v1/scores/calculate                   # 触发评分计算
```

### 5.2 响应格式标准
```python
# 成功响应
{
    "code": 200,
    "message": "success",
    "data": {...},
    "timestamp": "2025-01-15T10:30:00Z"
}

# 错误响应
{
    "code": 400,
    "message": "参数错误",
    "error": "station_id不能为空",
    "timestamp": "2025-01-15T10:30:00Z"
}
```

## 六、开发计划

### Day 1-2: 项目初始化和数据模型设计
- [x] 创建项目结构
- [x] 配置开发环境
- [x] 设计数据库表结构
- [x] 实现SQLAlchemy模型
- [x] 配置数据库连接

### Day 3-4: 评分算法核心实现
- [ ] 实现数据分析器
- [ ] 开发评分计算算法
- [ ] 实现缓存机制
- [ ] 编写单元测试

### Day 5-6: API开发和数据存储
- [ ] 开发FastAPI接口
- [ ] 实现定时任务
- [ ] 完成数据存储逻辑
- [ ] API测试和文档

## 七、部署方案

### 7.1 Docker配置
```dockerfile
FROM python:3.9-slim
WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt
COPY . .
EXPOSE 8000
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
```

### 7.2 环境变量
```env
DATABASE_URL=mysql://user:pass@localhost/chargepile-v3.0
REDIS_URL=redis://localhost:6379/0
SECRET_KEY=your-secret-key
DEBUG=False
```

## 八、监控和维护

### 8.1 日志记录
- API访问日志
- 评分计算日志
- 错误异常日志

### 8.2 性能监控
- API响应时间
- 数据库查询性能
- 缓存命中率

### 8.3 数据质量
- 数据完整性检查
- 异常值监控
- 计算结果验证

## 九、后续扩展

### 9.1 功能扩展
- 增加更多评分维度
- 支持自定义权重配置
- 添加预警机制

### 9.2 性能优化
- 数据分片存储
- 异步计算优化
- 缓存策略优化

### 9.3 集成扩展
- 消息队列集成
- 数据可视化
- 报表生成
