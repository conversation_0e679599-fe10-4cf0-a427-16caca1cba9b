# 充电场站评分系统 - 优化后的指标体系和前端交互方案

## 一、完整指标体系分类

### 1.1 运营效率指标 (50%权重)
```python
OPERATION_EFFICIENCY_METRICS = {
    "daily_revenue_rate": {
        "名称": "日均收益率",
        "计算公式": "total_fees / 运营天数",
        "单位": "元/天",
        "权重": 30,
        "说明": "衡量场站每天的平均收益能力"
    },
    "device_utilization": {
        "名称": "设备利用率", 
        "计算公式": "充电订单数 / (设备数量 × 运营天数)",
        "单位": "%",
        "权重": 25,
        "说明": "衡量设备的使用频率和效率"
    },
    "revenue_per_device": {
        "名称": "单设备产出",
        "计算公式": "total_fees / dc_ac_num", 
        "单位": "元/设备",
        "权重": 25,
        "说明": "衡量单个设备的收益贡献"
    },
    "completion_rate": {
        "名称": "充电完成率",
        "计算公式": "完成订单数 / 总订单数",
        "单位": "%", 
        "权重": 20,
        "说明": "衡量服务的可靠性和成功率"
    }
}
```

### 1.2 服务质量指标 (30%权重)
```python
SERVICE_QUALITY_METRICS = {
    "avg_charge_time": {
        "名称": "平均充电时长",
        "计算公式": "AVG(timelong)",
        "单位": "小时",
        "权重": 25,
        "说明": "衡量充电服务的效率，时长适中为佳"
    },
    "avg_charge_power": {
        "名称": "平均充电量",
        "计算公式": "AVG(charge_power)",
        "单位": "kWh", 
        "权重": 30,
        "说明": "衡量单次充电的电量，反映服务价值"
    },
    "user_retention_rate": {
        "名称": "用户复购率",
        "计算公式": "重复使用用户数 / 总用户数",
        "单位": "%",
        "权重": 30,
        "说明": "衡量用户满意度和忠诚度"
    },
    "failure_rate": {
        "名称": "故障率",
        "计算公式": "异常结束订单数 / 总订单数", 
        "单位": "%",
        "权重": 15,
        "说明": "衡量设备和服务的可靠性，越低越好"
    }
}
```

### 1.3 稳定性指标 (20%权重)
```python
STABILITY_METRICS = {
    "order_growth_trend": {
        "名称": "订单增长趋势",
        "计算公式": "(近期订单量 - 历史订单量) / 历史订单量 × 100",
        "单位": "%",
        "权重": 20,
        "说明": "衡量业务发展趋势和市场表现"
    },
    "revenue_stability": {
        "名称": "收益稳定性",
        "计算公式": "1 - (收益标准差 / 收益均值)",
        "单位": "%",
        "权重": 25,
        "说明": "衡量收益的波动程度，越稳定越好"
    },
    "operation_continuity": {
        "名称": "运营连续性", 
        "计算公式": "连续运营天数 / 总天数",
        "单位": "%",
        "权重": 20,
        "说明": "衡量场站运营的持续性"
    },
    "device_health_rate": {
        "名称": "设备健康度",
        "计算公式": "正常运行设备数 / 总设备数",
        "单位": "%", 
        "权重": 20,
        "说明": "衡量设备的整体健康状况"
    },
    "time_balance_index": {
        "名称": "时段均衡性",
        "计算公式": "基于不同时段利用率方差的标准化指数",
        "单位": "指数",
        "权重": 15,
        "说明": "衡量不同时段利用率的均衡程度"
    }
}
```

## 二、数据库表结构优化

### 2.1 station_score_trends表的作用
```sql
-- station_score_trends 专门存储历史变化趋势
CREATE TABLE station_score_trends (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    station_id VARCHAR(64) NOT NULL,
    stat_period VARCHAR(20) NOT NULL,
    
    -- 评分变化趋势
    total_score DECIMAL(5,2) NOT NULL COMMENT '当期总评分',
    prev_total_score DECIMAL(5,2) COMMENT '上期总评分', 
    score_change DECIMAL(6,2) COMMENT '评分变化值',
    score_change_rate DECIMAL(5,2) COMMENT '评分变化率(%)',
    
    -- 排名变化趋势
    city_rank INT COMMENT '当期城市排名',
    prev_city_rank INT COMMENT '上期城市排名',
    city_rank_change INT COMMENT '城市排名变化',
    
    -- 各维度变化趋势
    operation_score_change DECIMAL(6,2) COMMENT '运营效率评分变化',
    service_score_change DECIMAL(6,2) COMMENT '服务质量评分变化', 
    stability_score_change DECIMAL(6,2) COMMENT '稳定性评分变化',
    
    -- 关键指标变化
    revenue_change_rate DECIMAL(6,2) COMMENT '收益变化率',
    order_change_rate DECIMAL(6,2) COMMENT '订单量变化率',
    user_change_rate DECIMAL(6,2) COMMENT '用户数变化率',
    
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE KEY uk_station_period_trend (station_id, stat_period),
    INDEX idx_score_change (score_change DESC),
    INDEX idx_period_trend (stat_period)
) COMMENT='场站评分历史趋势表';
```

### 2.2 与开发计划的结合
```python
# Day 3-4: 评分算法实现时
ALGORITHM_IMPLEMENTATION = {
    "计算当期评分": "基于13个指标计算当期评分",
    "对比历史数据": "与上期数据对比，计算变化趋势",
    "更新trends表": "将变化数据写入station_score_trends表",
    "生成趋势分析": "为前端提供趋势图表数据"
}

# Day 5-6: API开发时
API_DEVELOPMENT = {
    "历史趋势接口": "GET /api/v1/stations/{id}/trends",
    "对比分析接口": "GET /api/v1/stations/{id}/compare", 
    "趋势统计接口": "GET /api/v1/scores/trend-stats"
}
```

## 三、Redis使用策略重新评估

### 3.1 是否需要Redis？
```python
# 基于月度更新的特点，Redis的必要性分析
REDIS_NECESSITY_ANALYSIS = {
    "数据更新频率": "月度更新，不是实时数据",
    "查询复杂度": "主要是简单的表查询，MySQL性能足够",
    "并发压力": "内部系统，并发量不高",
    "数据量": "场站数量有限，查询响应快",
    "结论": "可以不使用Redis，直接查询MySQL"
}

# 简化架构的优势
SIMPLIFIED_ARCHITECTURE_BENEFITS = {
    "降低复杂度": "减少Redis维护成本",
    "数据一致性": "避免缓存同步问题", 
    "开发效率": "专注核心业务逻辑",
    "运维简单": "只需维护MySQL一个存储"
}
```

### 3.2 性能优化替代方案
```sql
-- 通过数据库优化保证查询性能
-- 1. 合理的索引设计
CREATE INDEX idx_station_latest_score ON station_scores (station_id, stat_period DESC);
CREATE INDEX idx_city_ranking ON station_scores (city_name, total_score DESC, stat_period);

-- 2. 视图优化常用查询
CREATE VIEW v_latest_station_scores AS
SELECT * FROM station_scores 
WHERE stat_period = (SELECT MAX(stat_period) FROM station_scores);

-- 3. 分区表提升查询性能
ALTER TABLE station_scores 
PARTITION BY RANGE (YEAR(STR_TO_DATE(stat_period, '%Y-%m')));
```

## 四、前端交互方案设计

### 4.1 API接口设计（无Redis版本）
```python
# 核心API接口
FRONTEND_API_DESIGN = {
    "场站详情页": {
        "接口": "GET /api/v1/stations/{station_id}/detail",
        "返回": "当期评分 + 详细指标 + 历史趋势",
        "缓存": "MySQL查询结果，响应时间<200ms"
    },
    "历史趋势": {
        "接口": "GET /api/v1/stations/{station_id}/trends?months=12", 
        "返回": "12个月的评分变化趋势",
        "用途": "绘制趋势图表"
    },
    "指标对比": {
        "接口": "GET /api/v1/stations/{station_id}/compare?target_period=2024-12",
        "返回": "当期与指定期间的指标对比",
        "用途": "同比/环比分析"
    }
}
```

### 4.2 前端数据获取方式
```javascript
// 推荐的前端数据获取方式
const StationDetailAPI = {
    // 1. 场站详情页 - 一次性获取所有数据
    async getStationDetail(stationId) {
        const response = await fetch(`/api/v1/stations/${stationId}/detail`);
        return response.json();
        // 返回格式：
        // {
        //   basic_info: {...},
        //   current_score: {...},
        //   detailed_metrics: {...},
        //   recent_trends: [...] // 最近6个月趋势
        // }
    },
    
    // 2. 历史趋势图表 - 按需加载
    async getTrendData(stationId, months = 12) {
        const response = await fetch(`/api/v1/stations/${stationId}/trends?months=${months}`);
        return response.json();
    },
    
    // 3. 指标对比分析 - 交互式查询
    async getCompareData(stationId, targetPeriod) {
        const response = await fetch(`/api/v1/stations/${stationId}/compare?target_period=${targetPeriod}`);
        return response.json();
    }
};
```

### 4.3 前端展示组件建议
```javascript
// 推荐的前端组件结构
const StationDetailComponents = {
    "基础信息卡片": {
        "数据源": "basic_info",
        "展示": "场站名称、地址、设备数量等"
    },
    "评分仪表盘": {
        "数据源": "current_score.total_score", 
        "展示": "总分 + 等级 + 排名"
    },
    "三维雷达图": {
        "数据源": "current_score.dimension_scores",
        "展示": "运营效率、服务质量、稳定性"
    },
    "详细指标表格": {
        "数据源": "detailed_metrics",
        "展示": "13个具体指标的数值和说明"
    },
    "历史趋势图": {
        "数据源": "recent_trends",
        "展示": "评分变化曲线图"
    },
    "同比环比分析": {
        "数据源": "compare_data",
        "展示": "与上月/去年同期对比"
    }
};
```

## 五、前端合作方式建议

### 5.1 接口文档规范
```yaml
# OpenAPI 3.0 规范
openapi: 3.0.0
info:
  title: 充电场站评分系统API
  version: 1.0.0

paths:
  /api/v1/stations/{station_id}/detail:
    get:
      summary: 获取场站详细评分信息
      parameters:
        - name: station_id
          in: path
          required: true
          schema:
            type: string
        - name: include_trends
          in: query
          schema:
            type: boolean
            default: true
      responses:
        200:
          description: 成功返回场站详情
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/StationDetail'
```

### 5.2 数据格式标准化
```json
{
  "code": 200,
  "message": "success", 
  "data": {
    "basic_info": {
      "station_id": "123",
      "station_name": "北京朝阳充电站",
      "city_name": "北京市",
      "address": "朝阳区xxx路xxx号",
      "device_count": 8,
      "install_date": "2023-06-15"
    },
    "current_score": {
      "total_score": 85.6,
      "grade": "A", 
      "city_rank": 5,
      "national_rank": 23,
      "stat_period": "2025-01",
      "dimension_scores": {
        "operation_efficiency": 88.5,
        "service_quality": 82.3,
        "stability": 84.2
      }
    },
    "detailed_metrics": {
      "operation_efficiency": {
        "daily_revenue_rate": 506.67,
        "device_utilization": 78.5,
        "revenue_per_device": 5700.25,
        "completion_rate": 95.2
      },
      "service_quality": {
        "avg_charge_time": 2.5,
        "avg_charge_power": 45.8,
        "user_retention_rate": 68.3,
        "failure_rate": 4.8
      },
      "stability": {
        "order_growth_trend": 12.5,
        "revenue_stability": 85.7,
        "operation_continuity": 92.1,
        "device_health_rate": 87.5,
        "time_balance_index": 76.3
      }
    },
    "recent_trends": [
      {
        "period": "2025-01",
        "total_score": 85.6,
        "score_change": 2.3,
        "rank_change": -2
      },
      {
        "period": "2024-12", 
        "total_score": 83.3,
        "score_change": -1.2,
        "rank_change": 1
      }
    ]
  },
  "timestamp": "2025-01-15T10:30:00Z"
}
```

### 5.3 开发协作流程
```python
COLLABORATION_WORKFLOW = {
    "第1步": "后端提供API文档和Mock数据",
    "第2步": "前端基于文档开发界面和交互",
    "第3步": "后端完成真实接口开发",
    "第4步": "联调测试和接口优化",
    "第5步": "性能测试和上线部署"
}

# Mock数据提供
MOCK_DATA_STRATEGY = {
    "工具": "使用FastAPI自动生成OpenAPI文档",
    "数据": "提供真实格式的示例数据",
    "环境": "搭建Mock服务器供前端开发使用"
}
```

## 六、总结

### 6.1 优化后的架构特点
- **简化架构**: 去除Redis，直接使用MySQL
- **完整指标**: 13个指标覆盖运营的各个方面
- **历史趋势**: 专门的trends表支持趋势分析
- **前端友好**: 标准化的API接口和数据格式

### 6.2 开发优势
- **降低复杂度**: 减少技术栈，专注业务逻辑
- **提升效率**: 标准化接口，前后端并行开发
- **易于维护**: 简单的架构，便于后期维护
- **扩展性好**: 预留扩展接口，支持未来功能增加
