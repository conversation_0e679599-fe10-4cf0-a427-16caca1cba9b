# 充电场站评分系统 - 前端API接口详细设计

## 一、核心接口设计（无Redis版本）

### 1.1 场站详情接口
**接口**: `GET /api/v1/stations/{station_id}/detail`

**描述**: 获取场站的完整评分信息，包含当期评分、详细指标和近期趋势

**请求参数**:
```python
{
    "station_id": "123",                    # 路径参数，场站ID
    "include_trends": true,                 # 可选，是否包含趋势数据
    "trend_months": 6                       # 可选，趋势数据月数，默认6个月
}
```

**响应示例**:
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "basic_info": {
            "station_id": "123",
            "station_name": "北京朝阳充电站",
            "city_name": "北京市",
            "address": "朝阳区xxx路xxx号",
            "device_count": 8,
            "install_date": "2023-06-15",
            "run_date": "2023-07-01",
            "status": 4,
            "longitude": "116.4074",
            "latitude": "39.9042"
        },
        "current_score": {
            "stat_period": "2025-01",
            "total_score": 85.6,
            "grade": "A",
            "city_rank": 5,
            "national_rank": 23,
            "dimension_scores": {
                "operation_efficiency": 88.5,
                "service_quality": 82.3,
                "stability": 84.2
            },
            "update_time": "2025-02-01T02:00:00Z"
        },
        "detailed_metrics": {
            "operation_efficiency": {
                "daily_revenue_rate": {
                    "value": 506.67,
                    "unit": "元/天",
                    "description": "日均收益率",
                    "weight": 30
                },
                "device_utilization": {
                    "value": 78.5,
                    "unit": "%",
                    "description": "设备利用率",
                    "weight": 25
                },
                "revenue_per_device": {
                    "value": 5700.25,
                    "unit": "元/设备",
                    "description": "单设备产出",
                    "weight": 25
                },
                "completion_rate": {
                    "value": 95.2,
                    "unit": "%", 
                    "description": "充电完成率",
                    "weight": 20
                }
            },
            "service_quality": {
                "avg_charge_time": {
                    "value": 2.5,
                    "unit": "小时",
                    "description": "平均充电时长",
                    "weight": 25
                },
                "avg_charge_power": {
                    "value": 45.8,
                    "unit": "kWh",
                    "description": "平均充电量",
                    "weight": 30
                },
                "user_retention_rate": {
                    "value": 68.3,
                    "unit": "%",
                    "description": "用户复购率",
                    "weight": 30
                },
                "failure_rate": {
                    "value": 4.8,
                    "unit": "%",
                    "description": "故障率",
                    "weight": 15
                }
            },
            "stability": {
                "order_growth_trend": {
                    "value": 12.5,
                    "unit": "%",
                    "description": "订单增长趋势",
                    "weight": 20
                },
                "revenue_stability": {
                    "value": 85.7,
                    "unit": "%",
                    "description": "收益稳定性",
                    "weight": 25
                },
                "operation_continuity": {
                    "value": 92.1,
                    "unit": "%",
                    "description": "运营连续性",
                    "weight": 20
                },
                "device_health_rate": {
                    "value": 87.5,
                    "unit": "%",
                    "description": "设备健康度",
                    "weight": 20
                },
                "time_balance_index": {
                    "value": 76.3,
                    "unit": "指数",
                    "description": "时段均衡性",
                    "weight": 15
                }
            }
        },
        "basic_statistics": {
            "total_orders": 1250,
            "completed_orders": 1190,
            "total_revenue": 45600.50,
            "operating_days": 90,
            "unique_users": 456,
            "repeat_users": 312
        },
        "recent_trends": [
            {
                "period": "2025-01",
                "total_score": 85.6,
                "score_change": 2.3,
                "score_change_rate": 2.8,
                "city_rank": 5,
                "rank_change": -2
            },
            {
                "period": "2024-12",
                "total_score": 83.3,
                "score_change": -1.2,
                "score_change_rate": -1.4,
                "city_rank": 7,
                "rank_change": 1
            }
        ]
    },
    "timestamp": "2025-01-15T10:30:00Z"
}
```

### 1.2 历史趋势接口
**接口**: `GET /api/v1/stations/{station_id}/trends`

**描述**: 获取场站的历史评分趋势数据，用于绘制趋势图表

**请求参数**:
```python
{
    "months": 12,                           # 可选，获取月数，默认12个月
    "dimension": "all",                     # 可选，维度 all/operation/service/stability
    "include_details": false                # 可选，是否包含详细指标趋势
}
```

**响应示例**:
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "station_id": "123",
        "station_name": "北京朝阳充电站",
        "trend_period": "2024-02 to 2025-01",
        "trends": [
            {
                "period": "2025-01",
                "total_score": 85.6,
                "operation_score": 88.5,
                "service_score": 82.3,
                "stability_score": 84.2,
                "city_rank": 5,
                "national_rank": 23,
                "total_revenue": 45600.50,
                "total_orders": 1250
            },
            {
                "period": "2024-12",
                "total_score": 83.3,
                "operation_score": 86.2,
                "service_score": 80.1,
                "stability_score": 82.8,
                "city_rank": 7,
                "national_rank": 28,
                "total_revenue": 42300.20,
                "total_orders": 1180
            }
        ],
        "summary": {
            "avg_score": 82.5,
            "max_score": 85.6,
            "min_score": 78.2,
            "score_trend": "上升",
            "best_rank": 5,
            "worst_rank": 12
        }
    },
    "timestamp": "2025-01-15T10:30:00Z"
}
```

### 1.3 对比分析接口
**接口**: `GET /api/v1/stations/{station_id}/compare`

**描述**: 对比分析场站在不同时期的表现

**请求参数**:
```python
{
    "base_period": "2025-01",               # 基准期间，默认最新
    "compare_period": "2024-12",            # 对比期间，必填
    "compare_type": "month"                 # 对比类型 month/quarter/year
}
```

**响应示例**:
```json
{
    "code": 200,
    "message": "success", 
    "data": {
        "station_id": "123",
        "station_name": "北京朝阳充电站",
        "comparison": {
            "base_period": "2025-01",
            "compare_period": "2024-12",
            "compare_type": "环比"
        },
        "score_comparison": {
            "total_score": {
                "base": 85.6,
                "compare": 83.3,
                "change": 2.3,
                "change_rate": 2.8,
                "trend": "上升"
            },
            "operation_efficiency": {
                "base": 88.5,
                "compare": 86.2,
                "change": 2.3,
                "change_rate": 2.7,
                "trend": "上升"
            },
            "service_quality": {
                "base": 82.3,
                "compare": 80.1,
                "change": 2.2,
                "change_rate": 2.7,
                "trend": "上升"
            },
            "stability": {
                "base": 84.2,
                "compare": 82.8,
                "change": 1.4,
                "change_rate": 1.7,
                "trend": "上升"
            }
        },
        "metrics_comparison": {
            "daily_revenue_rate": {
                "base": 506.67,
                "compare": 470.00,
                "change": 36.67,
                "change_rate": 7.8,
                "trend": "上升"
            },
            "device_utilization": {
                "base": 78.5,
                "compare": 75.2,
                "change": 3.3,
                "change_rate": 4.4,
                "trend": "上升"
            }
        },
        "business_comparison": {
            "total_orders": {
                "base": 1250,
                "compare": 1180,
                "change": 70,
                "change_rate": 5.9,
                "trend": "上升"
            },
            "total_revenue": {
                "base": 45600.50,
                "compare": 42300.20,
                "change": 3300.30,
                "change_rate": 7.8,
                "trend": "上升"
            }
        },
        "analysis": {
            "improvement_areas": ["服务质量有所提升", "设备利用率增长明显"],
            "concern_areas": ["时段均衡性需要关注"],
            "recommendations": ["建议优化非高峰期运营策略"]
        }
    },
    "timestamp": "2025-01-15T10:30:00Z"
}
```

## 二、前端组件数据绑定

### 2.1 Vue.js 组件示例
```vue
<template>
  <div class="station-detail">
    <!-- 基础信息卡片 -->
    <StationInfoCard :data="stationData.basic_info" />
    
    <!-- 评分仪表盘 -->
    <ScoreDashboard 
      :score="stationData.current_score.total_score"
      :grade="stationData.current_score.grade"
      :rank="stationData.current_score.city_rank"
    />
    
    <!-- 三维雷达图 -->
    <RadarChart :data="stationData.current_score.dimension_scores" />
    
    <!-- 详细指标表格 -->
    <MetricsTable :data="stationData.detailed_metrics" />
    
    <!-- 历史趋势图 -->
    <TrendChart :data="stationData.recent_trends" />
  </div>
</template>

<script>
export default {
  name: 'StationDetail',
  data() {
    return {
      stationData: {},
      loading: false
    }
  },
  async mounted() {
    await this.loadStationData();
  },
  methods: {
    async loadStationData() {
      this.loading = true;
      try {
        const response = await this.$api.getStationDetail(this.$route.params.id);
        this.stationData = response.data;
      } catch (error) {
        this.$message.error('加载数据失败');
      } finally {
        this.loading = false;
      }
    }
  }
}
</script>
```

### 2.2 React 组件示例
```jsx
import React, { useState, useEffect } from 'react';
import { getStationDetail } from '../api/stationApi';

const StationDetail = ({ stationId }) => {
  const [stationData, setStationData] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadStationData();
  }, [stationId]);

  const loadStationData = async () => {
    try {
      setLoading(true);
      const response = await getStationDetail(stationId);
      setStationData(response.data);
    } catch (error) {
      console.error('加载数据失败:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) return <div>加载中...</div>;
  if (!stationData) return <div>暂无数据</div>;

  return (
    <div className="station-detail">
      <StationInfoCard data={stationData.basic_info} />
      <ScoreDashboard 
        score={stationData.current_score.total_score}
        grade={stationData.current_score.grade}
        rank={stationData.current_score.city_rank}
      />
      <RadarChart data={stationData.current_score.dimension_scores} />
      <MetricsTable data={stationData.detailed_metrics} />
      <TrendChart data={stationData.recent_trends} />
    </div>
  );
};

export default StationDetail;
```

## 三、API客户端封装

### 3.1 JavaScript API客户端
```javascript
class StationEvaluationAPI {
  constructor(baseURL = '/api/v1') {
    this.baseURL = baseURL;
  }

  async request(url, options = {}) {
    const response = await fetch(`${this.baseURL}${url}`, {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      },
      ...options
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return response.json();
  }

  // 获取场站详情
  async getStationDetail(stationId, options = {}) {
    const params = new URLSearchParams({
      include_trends: options.includeTrends || true,
      trend_months: options.trendMonths || 6
    });
    
    return this.request(`/stations/${stationId}/detail?${params}`);
  }

  // 获取历史趋势
  async getStationTrends(stationId, options = {}) {
    const params = new URLSearchParams({
      months: options.months || 12,
      dimension: options.dimension || 'all',
      include_details: options.includeDetails || false
    });
    
    return this.request(`/stations/${stationId}/trends?${params}`);
  }

  // 获取对比分析
  async getStationCompare(stationId, comparePeriod, options = {}) {
    const params = new URLSearchParams({
      base_period: options.basePeriod || '',
      compare_period: comparePeriod,
      compare_type: options.compareType || 'month'
    });
    
    return this.request(`/stations/${stationId}/compare?${params}`);
  }

  // 获取场站列表
  async getStationList(options = {}) {
    const params = new URLSearchParams({
      city_name: options.cityName || '',
      grade: options.grade || '',
      page: options.page || 1,
      size: options.size || 20,
      sort: options.sort || 'total_score',
      order: options.order || 'desc'
    });
    
    return this.request(`/stations/scores?${params}`);
  }

  // 获取排行榜
  async getRankings(options = {}) {
    const params = new URLSearchParams({
      scope: options.scope || 'city',
      city_name: options.cityName || '',
      top: options.top || 10,
      period: options.period || ''
    });
    
    return this.request(`/scores/rankings?${params}`);
  }
}

// 导出单例
export default new StationEvaluationAPI();
```

### 3.2 TypeScript 类型定义
```typescript
// 类型定义
export interface StationBasicInfo {
  station_id: string;
  station_name: string;
  city_name: string;
  address: string;
  device_count: number;
  install_date: string;
  run_date: string;
  status: number;
  longitude: string;
  latitude: string;
}

export interface CurrentScore {
  stat_period: string;
  total_score: number;
  grade: string;
  city_rank: number;
  national_rank: number;
  dimension_scores: {
    operation_efficiency: number;
    service_quality: number;
    stability: number;
  };
  update_time: string;
}

export interface MetricValue {
  value: number;
  unit: string;
  description: string;
  weight: number;
}

export interface DetailedMetrics {
  operation_efficiency: {
    daily_revenue_rate: MetricValue;
    device_utilization: MetricValue;
    revenue_per_device: MetricValue;
    completion_rate: MetricValue;
  };
  service_quality: {
    avg_charge_time: MetricValue;
    avg_charge_power: MetricValue;
    user_retention_rate: MetricValue;
    failure_rate: MetricValue;
  };
  stability: {
    order_growth_trend: MetricValue;
    revenue_stability: MetricValue;
    operation_continuity: MetricValue;
    device_health_rate: MetricValue;
    time_balance_index: MetricValue;
  };
}

export interface StationDetailResponse {
  basic_info: StationBasicInfo;
  current_score: CurrentScore;
  detailed_metrics: DetailedMetrics;
  basic_statistics: any;
  recent_trends: any[];
}
```

## 四、前端性能优化建议

### 4.1 数据缓存策略
```javascript
// 前端缓存管理
class DataCache {
  constructor() {
    this.cache = new Map();
    this.expireTime = 5 * 60 * 1000; // 5分钟过期
  }

  set(key, data) {
    this.cache.set(key, {
      data,
      timestamp: Date.now()
    });
  }

  get(key) {
    const item = this.cache.get(key);
    if (!item) return null;
    
    if (Date.now() - item.timestamp > this.expireTime) {
      this.cache.delete(key);
      return null;
    }
    
    return item.data;
  }

  clear() {
    this.cache.clear();
  }
}

// 使用缓存的API客户端
class CachedStationAPI extends StationEvaluationAPI {
  constructor() {
    super();
    this.cache = new DataCache();
  }

  async getStationDetail(stationId, options = {}) {
    const cacheKey = `station_detail_${stationId}`;
    const cached = this.cache.get(cacheKey);
    
    if (cached) {
      return cached;
    }

    const result = await super.getStationDetail(stationId, options);
    this.cache.set(cacheKey, result);
    return result;
  }
}
```

### 4.2 懒加载和分页
```javascript
// 懒加载趋势数据
const useLazyTrends = (stationId) => {
  const [trends, setTrends] = useState([]);
  const [loading, setLoading] = useState(false);

  const loadTrends = useCallback(async (months = 6) => {
    setLoading(true);
    try {
      const response = await api.getStationTrends(stationId, { months });
      setTrends(response.data.trends);
    } catch (error) {
      console.error('加载趋势数据失败:', error);
    } finally {
      setLoading(false);
    }
  }, [stationId]);

  return { trends, loading, loadTrends };
};
```

这样的设计确保了：
1. **简化架构**：去除Redis，直接使用MySQL
2. **完整数据**：一次API调用获取所有需要的数据
3. **前端友好**：标准化的数据格式，便于组件绑定
4. **性能优化**：合理的缓存策略和懒加载机制
