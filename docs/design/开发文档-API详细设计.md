# 充电场站评分系统 - API详细设计文档

## 一、API概览

### 1.1 基础信息
- **Base URL**: `http://localhost:8000/api/v1`
- **认证方式**: <PERSON><PERSON> (可选，后续扩展)
- **数据格式**: JSON
- **字符编码**: UTF-8

### 1.2 通用响应格式
```json
{
    "code": 200,
    "message": "success",
    "data": {},
    "timestamp": "2025-01-15T10:30:00Z"
}
```

### 1.3 错误码定义
```python
ERROR_CODES = {
    200: "成功",
    400: "请求参数错误",
    404: "资源不存在", 
    500: "服务器内部错误",
    1001: "场站不存在",
    1002: "评分数据未生成",
    1003: "计算任务进行中"
}
```

## 二、核心API接口

### 2.1 获取场站评分列表

**接口**: `GET /api/v1/stations/scores`

**描述**: 获取场站评分列表，支持分页和筛选

**请求参数**:
```python
{
    "city_name": "北京市",           # 可选，城市名称
    "grade": "A",                   # 可选，评分等级 S/A/B/C/D
    "min_score": 80.0,              # 可选，最低分数
    "max_score": 100.0,             # 可选，最高分数
    "page": 1,                      # 可选，页码，默认1
    "size": 20,                     # 可选，每页数量，默认20，最大100
    "sort": "total_score",          # 可选，排序字段
    "order": "desc"                 # 可选，排序方向 asc/desc
}
```

**响应示例**:
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "total": 156,
        "page": 1,
        "size": 20,
        "pages": 8,
        "items": [
            {
                "station_id": "123",
                "station_name": "北京朝阳充电站",
                "city_name": "北京市",
                "total_score": 85.6,
                "grade": "A",
                "city_rank": 5,
                "national_rank": 23,
                "scores": {
                    "operation_score": 88.5,
                    "service_score": 82.3,
                    "stability_score": 84.2
                },
                "basic_info": {
                    "device_count": 8,
                    "total_orders": 1250,
                    "total_revenue": 45600.50,
                    "operating_days": 90
                },
                "stat_period": "2025-01",
                "update_time": "2025-01-15T10:30:00Z"
            }
        ]
    },
    "timestamp": "2025-01-15T10:30:00Z"
}
```

### 2.2 获取单个场站详细评分

**接口**: `GET /api/v1/stations/{station_id}/score`

**描述**: 获取指定场站的详细评分信息

**路径参数**:
- `station_id`: 场站ID

**查询参数**:
```python
{
    "period": "2025-01",            # 可选，统计周期，默认最新
    "include_details": true         # 可选，是否包含详细指标
}
```

**响应示例**:
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "station_info": {
            "station_id": "123",
            "station_name": "北京朝阳充电站",
            "city_name": "北京市",
            "address": "北京市朝阳区xxx路xxx号",
            "device_count": 8,
            "install_date": "2023-06-15",
            "run_date": "2023-07-01",
            "status": 4
        },
        "evaluation": {
            "total_score": 85.6,
            "grade": "A",
            "city_rank": 5,
            "national_rank": 23,
            "stat_period": "2025-01",
            "scores": {
                "operation_score": 88.5,
                "service_score": 82.3,
                "stability_score": 84.2
            },
            "details": {
                "operation_metrics": {
                    "daily_revenue": 506.67,
                    "device_utilization": 78.5,
                    "completion_rate": 95.2
                },
                "service_metrics": {
                    "avg_charge_time": 2.5,
                    "avg_charge_power": 45.8,
                    "user_retention_rate": 68.3
                },
                "stability_metrics": {
                    "revenue_stability": 85.7,
                    "operation_continuity": 92.1
                }
            },
            "statistics": {
                "total_orders": 1250,
                "completed_orders": 1190,
                "total_revenue": 45600.50,
                "operating_days": 90,
                "unique_users": 456
            }
        }
    },
    "timestamp": "2025-01-15T10:30:00Z"
}
```

### 2.3 获取排行榜

**接口**: `GET /api/v1/scores/rankings`

**描述**: 获取场站评分排行榜

**请求参数**:
```python
{
    "scope": "city",                # 必填，范围 city/national
    "city_name": "北京市",          # scope=city时必填
    "top": 10,                      # 可选，排行数量，默认10，最大50
    "period": "2025-01"             # 可选，统计周期，默认最新
}
```

**响应示例**:
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "scope": "city",
        "city_name": "北京市",
        "period": "2025-01",
        "total_stations": 45,
        "rankings": [
            {
                "rank": 1,
                "station_id": "123",
                "station_name": "北京朝阳充电站",
                "total_score": 92.5,
                "grade": "S",
                "score_change": "+2.3",
                "rank_change": "+2"
            },
            {
                "rank": 2,
                "station_id": "124",
                "station_name": "北京海淀充电站",
                "total_score": 89.8,
                "grade": "A",
                "score_change": "-1.2",
                "rank_change": "-1"
            }
        ]
    },
    "timestamp": "2025-01-15T10:30:00Z"
}
```

### 2.4 获取评分统计

**接口**: `GET /api/v1/scores/statistics`

**描述**: 获取评分分布和统计信息

**请求参数**:
```python
{
    "period": "2025-01",            # 可选，统计周期
    "city_name": "北京市"           # 可选，城市筛选
}
```

**响应示例**:
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "period": "2025-01",
        "total_stations": 156,
        "grade_distribution": {
            "S": {"count": 12, "percentage": 7.7},
            "A": {"count": 45, "percentage": 28.8},
            "B": {"count": 67, "percentage": 43.0},
            "C": {"count": 28, "percentage": 17.9},
            "D": {"count": 4, "percentage": 2.6}
        },
        "score_distribution": {
            "90-100": 12,
            "80-89": 45,
            "70-79": 67,
            "60-69": 28,
            "0-59": 4
        },
        "city_statistics": [
            {
                "city_name": "北京市",
                "station_count": 45,
                "avg_score": 78.5,
                "top_grade_count": 8,
                "avg_revenue": 35600.0
            }
        ],
        "overall_metrics": {
            "avg_score": 76.8,
            "median_score": 78.2,
            "std_score": 12.5,
            "total_revenue": 5680000.0,
            "total_orders": 125000
        }
    },
    "timestamp": "2025-01-15T10:30:00Z"
}
```

### 2.5 触发评分计算

**接口**: `POST /api/v1/scores/calculate`

**描述**: 手动触发评分计算任务

**请求体**:
```json
{
    "period": "2025-01",            # 可选，计算周期，默认当前月
    "station_ids": ["123", "124"],  # 可选，指定场站，默认全部
    "force": false                  # 可选，是否强制重新计算
}
```

**响应示例**:
```json
{
    "code": 200,
    "message": "计算任务已启动",
    "data": {
        "batch_id": "calc_20250115_103000",
        "period": "2025-01",
        "station_count": 156,
        "estimated_duration": 300,
        "status": "RUNNING"
    },
    "timestamp": "2025-01-15T10:30:00Z"
}
```

### 2.6 获取计算任务状态

**接口**: `GET /api/v1/scores/calculate/{batch_id}/status`

**描述**: 查询评分计算任务状态

**响应示例**:
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "batch_id": "calc_20250115_103000",
        "status": "SUCCESS",
        "progress": {
            "total_stations": 156,
            "completed": 156,
            "failed": 0,
            "percentage": 100.0
        },
        "timing": {
            "start_time": "2025-01-15T10:30:00Z",
            "end_time": "2025-01-15T10:35:30Z",
            "duration_seconds": 330
        },
        "result": {
            "success_count": 156,
            "failed_count": 0,
            "error_message": null
        }
    },
    "timestamp": "2025-01-15T10:35:30Z"
}
```

## 三、数据模型定义

### 3.1 场站评分模型
```python
class StationScoreResponse(BaseModel):
    station_id: str
    station_name: str
    city_name: str
    total_score: float
    grade: str
    city_rank: Optional[int]
    national_rank: Optional[int]
    scores: Dict[str, float]
    basic_info: Dict[str, Any]
    stat_period: str
    update_time: datetime
```

### 3.2 分页模型
```python
class PaginatedResponse(BaseModel):
    total: int
    page: int
    size: int
    pages: int
    items: List[Any]
```

### 3.3 错误响应模型
```python
class ErrorResponse(BaseModel):
    code: int
    message: str
    error: Optional[str]
    timestamp: datetime
```

## 四、状态码说明

### 4.1 HTTP状态码
- `200`: 请求成功
- `400`: 请求参数错误
- `404`: 资源不存在
- `500`: 服务器内部错误

### 4.2 业务状态码
- `1001`: 场站不存在
- `1002`: 评分数据未生成
- `1003`: 计算任务进行中
- `1004`: 计算任务失败

## 五、限流和缓存

### 5.1 接口限流
```python
RATE_LIMITS = {
    "/api/v1/stations/scores": "100/minute",
    "/api/v1/scores/calculate": "5/minute",
    "default": "200/minute"
}
```

### 5.2 缓存策略
```python
CACHE_CONFIG = {
    "station_score": 3600,      # 1小时
    "rankings": 1800,           # 30分钟
    "statistics": 7200,         # 2小时
    "calculation_status": 300   # 5分钟
}
```

## 六、测试用例

### 6.1 正常场景测试
- 获取评分列表（有数据）
- 获取单个场站评分
- 获取排行榜
- 触发计算任务

### 6.2 异常场景测试
- 场站不存在
- 参数格式错误
- 数据库连接失败
- 计算任务重复提交

### 6.3 性能测试
- 并发请求测试
- 大数据量查询
- 缓存命中率测试
