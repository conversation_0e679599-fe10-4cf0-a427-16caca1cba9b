version: '3.8'

services:
  app:
    build: .
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=mysql+pymysql://root:password@mysql:3306/chargepile-v3.0
      - DEBUG=True
    depends_on:
      - mysql
    volumes:
      - ./logs:/app/logs
    restart: unless-stopped

  mysql:
    image: mysql:5.7
    environment:
      - MYSQL_ROOT_PASSWORD=password
      - MYSQL_DATABASE=chargepile-v3.0
      - MYSQL_CHARACTER_SET_SERVER=utf8mb4
      - MYSQL_COLLATION_SERVER=utf8mb4_unicode_ci
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./sql_base:/docker-entrypoint-initdb.d
    restart: unless-stopped

volumes:
  mysql_data:
