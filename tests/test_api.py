"""
API接口测试
"""
import pytest
from fastapi.testclient import TestClient
from app.main import app

client = TestClient(app)


def test_health_check():
    """测试健康检查接口"""
    response = client.get("/health")
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "healthy"
    assert "app_name" in data
    assert "version" in data


def test_root_endpoint():
    """测试根路径"""
    response = client.get("/")
    assert response.status_code == 200
    data = response.json()
    assert "message" in data
    assert "version" in data


def test_station_scores_endpoint():
    """测试场站评分列表接口"""
    response = client.get("/api/v1/stations/scores")
    assert response.status_code == 200
    data = response.json()
    assert data["code"] == 200
    assert "data" in data
    assert "timestamp" in data


def test_rankings_endpoint():
    """测试排行榜接口"""
    response = client.get("/api/v1/scores/rankings?scope=national&top=5")
    assert response.status_code == 200
    data = response.json()
    assert data["code"] == 200


def test_statistics_endpoint():
    """测试统计信息接口"""
    response = client.get("/api/v1/scores/statistics")
    assert response.status_code == 200
    data = response.json()
    assert data["code"] == 200


def test_invalid_station_detail():
    """测试无效场站ID"""
    response = client.get("/api/v1/stations/999999/detail")
    assert response.status_code == 404


def test_invalid_ranking_scope():
    """测试无效排行榜范围"""
    response = client.get("/api/v1/scores/rankings?scope=invalid")
    assert response.status_code == 422  # 参数验证错误
