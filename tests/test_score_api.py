"""
评分API测试
"""
import pytest
from fastapi.testclient import TestClient
from unittest.mock import Mock, patch
import json

from app.main import app

client = TestClient(app)


class TestScoreAPI:
    """评分API测试类"""
    
    @patch('app.services.score_service.ScoreService.trigger_calculation')
    @patch('app.database.get_db')
    def test_trigger_calculation_api(self, mock_get_db, mock_trigger_calc):
        """测试触发评分计算API"""
        # 模拟数据库会话
        mock_db = Mock()
        mock_get_db.return_value = mock_db
        
        # 模拟服务返回
        mock_trigger_calc.return_value = {
            "batch_id": "calc_20250115_123456_abcd1234",
            "period": "2025-01",
            "station_count": 10,
            "estimated_duration": 20,
            "status": "PENDING"
        }
        
        # 发送请求
        response = client.post(
            "/api/v1/scores/calculate",
            json={
                "period": "2025-01",
                "station_ids": None,
                "force": False
            }
        )
        
        # 验证响应
        assert response.status_code == 200
        data = response.json()
        assert data["code"] == 200
        assert data["message"] == "计算任务已启动"
        assert "batch_id" in data["data"]
        assert data["data"]["status"] == "PENDING"
        
        # 验证服务方法被调用
        mock_trigger_calc.assert_called_once_with(
            period="2025-01",
            station_ids=None,
            force=False
        )
    
    @patch('app.services.score_service.ScoreService.get_rankings')
    @patch('app.database.get_db')
    def test_get_rankings_api(self, mock_get_db, mock_get_rankings):
        """测试获取排行榜API"""
        # 模拟数据库会话
        mock_db = Mock()
        mock_get_db.return_value = mock_db
        
        # 模拟服务返回
        mock_get_rankings.return_value = {
            "scope": "national",
            "city_name": None,
            "period": "2025-01",
            "total_stations": 100,
            "rankings": [
                {
                    "rank": 1,
                    "station_id": "station_001",
                    "station_name": "测试场站1",
                    "total_score": 95.5,
                    "grade": "S",
                    "score_change": 2.3,
                    "rank_change": 1
                }
            ]
        }
        
        # 发送请求
        response = client.get("/api/v1/scores/rankings?scope=national&top=10")
        
        # 验证响应
        assert response.status_code == 200
        data = response.json()
        assert data["code"] == 200
        assert data["message"] == "success"
        assert "rankings" in data["data"]
        assert len(data["data"]["rankings"]) == 1
        assert data["data"]["rankings"][0]["rank"] == 1
    
    @patch('app.services.score_service.ScoreService.get_statistics')
    @patch('app.database.get_db')
    def test_get_statistics_api(self, mock_get_db, mock_get_statistics):
        """测试获取统计信息API"""
        # 模拟数据库会话
        mock_db = Mock()
        mock_get_db.return_value = mock_db
        
        # 模拟服务返回
        mock_get_statistics.return_value = {
            "period": "2025-01",
            "total_stations": 100,
            "grade_distribution": {
                "S": {"count": 10, "percentage": 10.0},
                "A": {"count": 30, "percentage": 30.0},
                "B": {"count": 40, "percentage": 40.0},
                "C": {"count": 15, "percentage": 15.0},
                "D": {"count": 5, "percentage": 5.0}
            },
            "score_distribution": {
                "90-100": 10,
                "80-89": 30,
                "70-79": 40,
                "60-69": 15,
                "0-59": 5
            },
            "overall_metrics": {
                "avg_score": 76.8,
                "median_score": 78.2,
                "std_score": 12.5,
                "total_revenue": 5680000.0,
                "total_orders": 125000
            }
        }
        
        # 发送请求
        response = client.get("/api/v1/scores/statistics")
        
        # 验证响应
        assert response.status_code == 200
        data = response.json()
        assert data["code"] == 200
        assert data["message"] == "success"
        assert "grade_distribution" in data["data"]
        assert "overall_metrics" in data["data"]
        assert data["data"]["total_stations"] == 100


class TestScoreCalculationWorkflow:
    """评分计算工作流测试"""
    
    def test_score_calculation_components(self):
        """测试评分计算组件是否正确导入"""
        from app.core.analyzer import DataAnalyzer
        from app.core.calculator import ScoreCalculator
        
        # 验证类可以正常实例化
        mock_db = Mock()
        analyzer = DataAnalyzer(mock_db)
        calculator = ScoreCalculator()
        
        assert analyzer is not None
        assert calculator is not None
        
        # 验证关键方法存在
        assert hasattr(analyzer, 'get_station_raw_data')
        assert hasattr(analyzer, 'calculate_operation_metrics')
        assert hasattr(analyzer, 'calculate_service_metrics')
        assert hasattr(analyzer, 'calculate_stability_metrics')
        
        assert hasattr(calculator, 'percentile_normalize')
        assert hasattr(calculator, 'calculate_total_score')
        assert hasattr(calculator, 'batch_calculate_scores')


if __name__ == "__main__":
    pytest.main([__file__])
