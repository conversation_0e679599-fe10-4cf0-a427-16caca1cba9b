"""
数据库连接测试
"""
import pytest
from sqlalchemy import text
from app.database import get_db_session, check_database_connection


def test_database_connection():
    """测试数据库连接"""
    assert check_database_connection() == True


def test_database_session():
    """测试数据库会话"""
    with get_db_session() as db:
        result = db.execute(text("SELECT 1 as test")).fetchone()
        assert result.test == 1


def test_database_models():
    """测试数据模型导入"""
    from app.models import (
        PileStation, 
        PileDevice, 
        StationScore, 
        StationScoreDetail,
        StationScoreTrend,
        ScoreCalculationLog
    )
    
    # 检查模型类是否正确定义
    assert hasattr(PileStation, '__tablename__')
    assert hasattr(StationScore, '__tablename__')
    assert hasattr(StationScoreDetail, '__tablename__')
    assert hasattr(StationScoreTrend, '__tablename__')
    assert hasattr(ScoreCalculationLog, '__tablename__')
