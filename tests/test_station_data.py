"""
场站数据读取测试
"""
import pytest
import sys,os
from sqlalchemy.orm import Session
# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.database import get_read_db, get_write_db
from app.models.station import PileStation
from app.models.order import PileOrderSnapshot
from app.models.score import StationScore


def test_read_stations():
    """测试读取场站数据"""
    with next(get_read_db()) as db:
        # 查询已投运的场站
        stations = db.query(PileStation).filter(
            PileStation.status == 4,  # 已投运
            PileStation.del_flag == 0  # 未删除
        ).limit(5).all()
        
        print(f"找到 {len(stations)} 个已投运场站:")
        for station in stations:
            print(f"- ID: {station.id}, 名称: {station.name}, 城市: {station.city_name}, 设备数: {station.dc_ac_num}")
        
        assert len(stations) >= 0  # 至少应该有一些场站


def test_read_orders():
    """测试读取订单数据"""
    with next(get_read_db()) as db:
        # 查询充电完成的订单
        orders = db.query(PileOrderSnapshot).filter(
            PileOrderSnapshot.status == 4  # 充电完成
        ).limit(5).all()
        
        print(f"找到 {len(orders)} 个充电完成订单:")
        for order in orders:
            print(f"- 订单号: {order.order_no}, 场站: {order.station_name}, 费用: {order.total_fees}, 时长: {order.timelong}")
        
        assert len(orders) >= 0


def test_station_orders_relation():
    """测试场站和订单的关联关系"""
    with next(get_read_db()) as db:
        # 找一个有订单的场站
        station = db.query(PileStation).filter(
            PileStation.status == 4,
            PileStation.del_flag == 0
        ).first()
        
        if station:
            print(f"测试场站: {station.name} (ID: {station.id})")
            
            # 查询该场站的订单
            orders = db.query(PileOrderSnapshot).filter(
                PileOrderSnapshot.station_id == station.id,
                PileOrderSnapshot.status == 4
            ).limit(10).all()
            
            print(f"该场站有 {len(orders)} 个充电完成订单")
            
            if orders:
                total_revenue = sum([float(order.total_fees) for order in orders if order.total_fees])
                total_power = sum([float(order.charge_power) for order in orders if order.charge_power])
                avg_time = sum([float(order.timelong) for order in orders if order.timelong]) / len(orders)
                
                print(f"- 总收益: {total_revenue:.2f} 元")
                print(f"- 总充电量: {total_power:.2f} kWh") 
                print(f"- 平均充电时长: {avg_time:.2f} 小时")


def test_write_database():
    """测试写数据库连接"""
    with next(get_write_db()) as db:
        # 测试查询（如果表不存在会报错）
        try:
            count = db.query(StationScore).count()
            print(f"写数据库中已有 {count} 条评分记录")
        except Exception as e:
            print(f"写数据库表可能还未创建: {e}")


def test_data_quality():
    """测试数据质量"""
    with next(get_read_db()) as db:
        # 检查数据质量
        print("=== 数据质量检查 ===")
        
        # 1. 场站数据质量
        total_stations = db.query(PileStation).filter(PileStation.del_flag == 0).count()
        active_stations = db.query(PileStation).filter(
            PileStation.status == 4,
            PileStation.del_flag == 0
        ).count()
        
        print(f"总场站数: {total_stations}")
        print(f"已投运场站数: {active_stations}")
        
        # 2. 订单数据质量
        total_orders = db.query(PileOrderSnapshot).count()
        completed_orders = db.query(PileOrderSnapshot).filter(
            PileOrderSnapshot.status == 4
        ).count()
        
        print(f"总订单数: {total_orders}")
        print(f"完成订单数: {completed_orders}")
        print(f"完成率: {(completed_orders/total_orders*100):.1f}%" if total_orders > 0 else "无订单数据")
        
        # 3. 数据完整性检查
        orders_with_fees = db.query(PileOrderSnapshot).filter(
            PileOrderSnapshot.status == 4,
            PileOrderSnapshot.total_fees > 0
        ).count()
        
        orders_with_power = db.query(PileOrderSnapshot).filter(
            PileOrderSnapshot.status == 4,
            PileOrderSnapshot.charge_power > 0
        ).count()
        
        print(f"有费用的订单: {orders_with_fees}")
        print(f"有充电量的订单: {orders_with_power}")


if __name__ == "__main__":
    # 直接运行测试
    print("开始测试数据库连接和数据读取...")
    
    try:
        test_read_stations()
        print("✓ 场站数据读取测试通过")
    except Exception as e:
        print(f"✗ 场站数据读取测试失败: {e}")
    
    try:
        test_read_orders()
        print("✓ 订单数据读取测试通过")
    except Exception as e:
        print(f"✗ 订单数据读取测试失败: {e}")
    
    try:
        test_station_orders_relation()
        print("✓ 场站订单关联测试通过")
    except Exception as e:
        print(f"✗ 场站订单关联测试失败: {e}")
    
    # try:
    #     test_write_database()
    #     print("✓ 写数据库连接测试通过")
    # except Exception as e:
    #     print(f"✗ 写数据库连接测试失败: {e}")
    
    # try:
    #     test_data_quality()
    #     print("✓ 数据质量检查完成")
    # except Exception as e:
    #     print(f"✗ 数据质量检查失败: {e}")
    
    print("测试完成！")
