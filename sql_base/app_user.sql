/*
 Navicat Premium Dump SQL

 Source Server         : mysql_domi
 Source Server Type    : MySQL
 Source Server Version : 50744 (5.7.44-log)
 Source Host           : rm-uf6pi2e7rri610wi3mo.mysql.rds.aliyuncs.com:3306
 Source Schema         : chargepile-v3.0

 Target Server Type    : MySQL
 Target Server Version : 50744 (5.7.44-log)
 File Encoding         : 65001

 Date: 07/08/2025 10:38:17
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for app_user
-- ----------------------------
DROP TABLE IF EXISTS `app_user`;
CREATE TABLE `app_user` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `name` varchar(60) DEFAULT NULL COMMENT '用户名',
  `active_code` varchar(60) CHARACTER SET utf8 DEFAULT NULL COMMENT '激活代码',
  `svcard_no` varchar(20) CHARACTER SET utf8 DEFAULT NULL COMMENT '当前储值卡号',
  `email` varchar(60) CHARACTER SET utf8 DEFAULT NULL COMMENT '邮箱',
  `image` varchar(1000) CHARACTER SET utf8 DEFAULT NULL COMMENT '图片',
  `organ_id` varchar(65) CHARACTER SET utf8 DEFAULT NULL COMMENT '机构id（运营商）',
  `phone` varchar(60) CHARACTER SET utf8 DEFAULT NULL COMMENT '手机',
  `address` varchar(165) CHARACTER SET utf8 DEFAULT NULL COMMENT '用户地址',
  `active` int(1) DEFAULT NULL COMMENT '1、已激活 2、已注销 9、黑名单',
  `sex` varchar(10) CHARACTER SET utf8 DEFAULT NULL COMMENT '性别，根据字典数据来',
  `birthday` varchar(255) CHARACTER SET utf8 DEFAULT NULL COMMENT '生日',
  `operator_id` varchar(128) CHARACTER SET utf8 DEFAULT NULL COMMENT '商户号',
  `open_id` varchar(65) CHARACTER SET utf8 DEFAULT NULL COMMENT '小程序用户的openid',
  `app_open_id` varchar(65) CHARACTER SET utf8 DEFAULT NULL COMMENT 'APP用户的openid',
  `alipay_pid` varchar(128) CHARACTER SET utf8 DEFAULT NULL COMMENT '支付宝账号pid',
  `create_time` datetime DEFAULT NULL COMMENT '创建日期',
  `del_flag` int(1) DEFAULT '0' COMMENT '删除标记 1:是 0:否',
  `ali_open_id` varchar(128) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `normal_operator_id` (`operator_id`)
) ENGINE=InnoDB AUTO_INCREMENT=248427 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='app用户';

SET FOREIGN_KEY_CHECKS = 1;
