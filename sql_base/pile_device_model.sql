/*
 Navicat Premium Dump SQL

 Source Server         : mysql_domi
 Source Server Type    : MySQL
 Source Server Version : 50744 (5.7.44-log)
 Source Host           : rm-uf6pi2e7rri610wi3mo.mysql.rds.aliyuncs.com:3306
 Source Schema         : chargepile-v3.0

 Target Server Type    : MySQL
 Target Server Version : 50744 (5.7.44-log)
 File Encoding         : 65001

 Date: 07/08/2025 10:36:07
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for pile_device_model
-- ----------------------------
DROP TABLE IF EXISTS `pile_device_model`;
CREATE TABLE `pile_device_model` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `type` varchar(100) DEFAULT NULL COMMENT '设备类型，字典表定义',
  `manufacture` varchar(200) DEFAULT NULL COMMENT '生产厂家',
  `name` varchar(200) DEFAULT NULL COMMENT '设备型号',
  `current` float(10,2) DEFAULT NULL COMMENT '额定电流',
  `comm_way` varchar(50) DEFAULT NULL COMMENT '数据通讯协议',
  `comm_address` varchar(50) DEFAULT NULL COMMENT '数据通讯地址',
  `voltage` float(10,2) DEFAULT NULL COMMENT '额定电压',
  `temperature` float(10,2) DEFAULT NULL COMMENT '温度',
  `contact` varchar(200) DEFAULT NULL COMMENT '联系方式',
  `remark` varchar(250) DEFAULT NULL COMMENT '备注',
  `img` varchar(1000) DEFAULT NULL COMMENT '图片地址',
  `rated_power` float(10,2) DEFAULT NULL COMMENT '额定功率',
  `device_total_power` float(5,2) DEFAULT '0.00' COMMENT '设备总电量(度)',
  `power_mode` varchar(50) DEFAULT NULL COMMENT '供电模式',
  `measure_grade` varchar(50) DEFAULT NULL COMMENT '计量准确度等级',
  `operating_life` varchar(50) DEFAULT NULL COMMENT '机械操作寿命',
  `prote_rated_current` varchar(50) DEFAULT NULL COMMENT '保护额定动作电流',
  `prote_rated_time` varchar(50) DEFAULT NULL COMMENT '保护额定动作时间',
  `avg_between_time` varchar(50) DEFAULT NULL COMMENT '平均故障间隔时间',
  `del_flag` int(1) DEFAULT '0' COMMENT '删除标记 1：是 0：否',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `create_by` varchar(30) DEFAULT NULL COMMENT '创建人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `update_by` varchar(30) DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='设备型号信息表';

SET FOREIGN_KEY_CHECKS = 1;
