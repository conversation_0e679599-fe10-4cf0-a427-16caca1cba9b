/*
 Navicat Premium Dump SQL

 Source Server         : mysql_domi
 Source Server Type    : MySQL
 Source Server Version : 50744 (5.7.44-log)
 Source Host           : rm-uf6pi2e7rri610wi3mo.mysql.rds.aliyuncs.com:3306
 Source Schema         : chargepile-v3.0

 Target Server Type    : MySQL
 Target Server Version : 50744 (5.7.44-log)
 File Encoding         : 65001

 Date: 07/08/2025 10:36:26
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for pile_device
-- ----------------------------
DROP TABLE IF EXISTS `pile_device`;
CREATE TABLE `pile_device` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `dept_id` bigint(20) DEFAULT NULL COMMENT '部门ID',
  `station_id` varchar(64) DEFAULT NULL COMMENT '电站id',
  `type` int(10) DEFAULT NULL COMMENT '设备类型 1: 充电车 2：充电枪', --只使用1充电车
  `model_id` bigint(20) DEFAULT NULL COMMENT '设备型号ID',
  `code` varchar(40) DEFAULT NULL COMMENT '设备编码',
  `bms_sn` varchar(255) DEFAULT NULL COMMENT 'BMS关联的sn   (vin)', --以这个作为设备名称
  `name` varchar(200) DEFAULT NULL COMMENT '设备名称', --暂不使用
  `parent_id` bigint(20) DEFAULT NULL COMMENT '上级ID',
  `ac_or_dc` varchar(100) DEFAULT NULL COMMENT '交直流类型 字典表ac_or_dc',
  `operator_id` varchar(64) DEFAULT NULL COMMENT '组织机构代码',
  `install_date` datetime DEFAULT NULL COMMENT '安装日期',
  `run_date` datetime DEFAULT NULL COMMENT '运行日期',
  `status` varchar(100) DEFAULT '1' COMMENT '设备状态，字典类型device_status',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `del_flag` int(1) DEFAULT NULL COMMENT '删除标记 1：是 0: 否',
  `connector_id` varchar(40) DEFAULT NULL COMMENT '合肥充电设备编码',
  `device_type` int(2) DEFAULT NULL COMMENT '国标桩类型 1=G3, 2=G1/2, 3=自研',
  PRIMARY KEY (`id`),
  KEY `bms_sn_index` (`bms_sn`),
  KEY `code_index` (`code`),
  KEY `connector_id_index` (`connector_id`),
  KEY `station_id_index` (`station_id`)
) ENGINE=InnoDB AUTO_INCREMENT=4323 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='设备表';

SET FOREIGN_KEY_CHECKS = 1;
