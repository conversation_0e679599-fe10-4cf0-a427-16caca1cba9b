/*
 Navicat Premium Dump SQL

 Source Server         : mysql_domi
 Source Server Type    : MySQL
 Source Server Version : 50744 (5.7.44-log)
 Source Host           : rm-uf6pi2e7rri610wi3mo.mysql.rds.aliyuncs.com:3306
 Source Schema         : chargepile-v3.0

 Target Server Type    : MySQL
 Target Server Version : 50744 (5.7.44-log)
 File Encoding         : 65001

 Date: 07/08/2025 10:34:02
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for pile_order
-- ----------------------------
DROP TABLE IF EXISTS `pile_order`;
CREATE TABLE `pile_order` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '订单id',
  `no` varchar(20) DEFAULT NULL COMMENT '订单编号',
  `pay_method` tinyint(2) DEFAULT NULL COMMENT '1微信预支付 2支付宝预支付 3余额支付 4充电卡 5余额充电卡组合支付',
  `pay_account` varchar(40) DEFAULT NULL COMMENT '支付账户',
  `pay_type` tinyint(2) DEFAULT '1' COMMENT '支付类型。1：个人，2：企业',
  `person_user_id` bigint(20) DEFAULT NULL COMMENT '个人用户ID',
  `operator_id` varchar(40) DEFAULT NULL COMMENT '运营商ID',
  `station_id` varchar(40) DEFAULT NULL COMMENT '充电站ID',
  `pile_no` varchar(40) DEFAULT NULL COMMENT '充电桩编号',
  `gun_id` varchar(40) DEFAULT NULL COMMENT '充电枪id', --暂时不用
  `start_time` datetime DEFAULT NULL COMMENT '开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '完成时间',
  `start_soc` int(3) DEFAULT NULL COMMENT '被充汽车开始soc',
  `end_soc` int(3) DEFAULT NULL COMMENT '被充汽车结束soc',
  `timelong` float DEFAULT NULL COMMENT '时长',
  `charge_power` float(8,4) DEFAULT NULL COMMENT '用电量',
  `charge_fees` float(8,4) DEFAULT NULL COMMENT '充电费用',
  `server_fees` float(8,4) DEFAULT NULL COMMENT '服务费',
  `capital_fees` float(8,2) DEFAULT NULL COMMENT '本金费用', --暂时不用
  `give_fees` float(8,2) DEFAULT NULL COMMENT '赠送费用', --暂时不用
  `total_fees` float(8,2) DEFAULT NULL COMMENT '总费用',
  `status` int(2) DEFAULT NULL COMMENT '订单状态:0:预约中 1:未接单 2:准备充电 3:充电中 4:充电完成 5:已取消', --只考虑充电已完成订单，可以对充电中订单是否达到完成状态做分析
  `pay_time` datetime DEFAULT NULL COMMENT '支付时间',
  `pay_status` tinyint(2) DEFAULT NULL COMMENT '支付状态:0、未支付 1、已支付',
  `overflow_policy` int(1) DEFAULT NULL COMMENT '充满电策略', --？？
  `overflow_para` int(4) DEFAULT NULL COMMENT '充满电策略参数', --？？
  `normal_end` int(10) DEFAULT NULL COMMENT '是否正常结束 根据订单结算上报的情况来区分', --？？
  `vin` varchar(40) DEFAULT NULL COMMENT '被充汽车车架号',
  `chargeable_capacity` double(10,2) DEFAULT NULL COMMENT '可充电量', --开启订单时，设备的可对外放电的电量
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_no` (`no`),
  KEY `normal_start_time` (`start_time`),
  KEY `idx_paytype_delflag_userid_stationid` (`pay_type`,`del_flag`,`user_id`,`station_id`),
  KEY `pile_order_idx_del_flag_operator_status` (`del_flag`,`operator_id`,`status`),
  KEY `normal_pile_no` (`pile_no`),
  KEY `normal_station_id` (`station_id`),
  KEY `normal_persion_user_id` (`person_user_id`),
  KEY `operator_id_index` (`operator_id`),
  KEY `normal_pay_account` (`pay_account`)
) ENGINE=InnoDB AUTO_INCREMENT=2983266 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='订单信息表';

-- ----------------------------
-- Triggers structure for table pile_order
-- ----------------------------
DROP TRIGGER IF EXISTS `insert_enterprise_user_order_data`;
delimiter ;;
CREATE TRIGGER `chargepile-v3.0`.`insert_enterprise_user_order_data` AFTER INSERT ON `pile_order` FOR EACH ROW IF new.pay_type=2 THEN
	insert into pile_enterprise_user_order values(null,new.id,(select  peu.id  from pile_enterprise_users peu inner join pile_enterprise pe on pe.id = peu.enterprise_id INNER JOIN pile_svcard ps on ps.operator_id = pe.id  where peu.user_id  = new.person_user_id and  ps.id = new.user_id and ps.status = 0 and peu.del_flag = 0 ));
END IF
;;
delimiter ;

SET FOREIGN_KEY_CHECKS = 1;
