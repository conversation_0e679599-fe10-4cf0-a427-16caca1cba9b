/*
 Navicat Premium Dump SQL

 Source Server         : mysql_domi
 Source Server Type    : MySQL
 Source Server Version : 50744 (5.7.44-log)
 Source Host           : rm-uf6pi2e7rri610wi3mo.mysql.rds.aliyuncs.com:3306
 Source Schema         : chargepile-v3.0

 Target Server Type    : MySQL
 Target Server Version : 50744 (5.7.44-log)
 File Encoding         : 65001

 Date: 07/08/2025 10:31:33
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for pile_station
-- ----------------------------
DROP TABLE IF EXISTS `pile_station`;
CREATE TABLE `pile_station` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `dept_id` bigint(20) DEFAULT NULL COMMENT '部门ID',
  `code` varchar(20) DEFAULT NULL COMMENT '电站代码',
  `name` varchar(200) DEFAULT NULL COMMENT '电站名称（汉）',
  `file_name` varchar(1000) DEFAULT NULL COMMENT '文件名称，即电站的图片路径，多个文件用,隔开',
  `longitude` varchar(30) DEFAULT NULL COMMENT '经度',
  `latitude` varchar(30) DEFAULT NULL COMMENT '纬度',
  `dc_ac_num` int(11) DEFAULT NULL COMMENT '交直流混合桩数量', --拥有充电车数量以这个为准
  `install_date` datetime DEFAULT NULL COMMENT '安装日期',
  `run_date` datetime DEFAULT NULL COMMENT '投运日期',
  `city_id` varchar(100) DEFAULT NULL COMMENT '城市id',
  `city_name` varchar(300) DEFAULT NULL COMMENT '城市名称',
  `address` varchar(200) DEFAULT NULL COMMENT '地址',
  `status` int(1) DEFAULT NULL COMMENT '状态 1:计划中 2:建设中 3:已建好未投运 4:已投运 5:关闭下线', --重点关注4（已投运）状态中的场站，可查看3（已建好未投运）的场站是否有设备
  `standard_id` bigint(20) DEFAULT NULL COMMENT '计费方案',
  `self_charge` int(1) DEFAULT '1' COMMENT '自行充电 1：是 0：否',
  `appointment_charge` tinyint(2) DEFAULT '0' COMMENT '是否支持预约充电 1:是 0:否',
  `appointment_charge_server_fees` float(8,4) DEFAULT NULL COMMENT '呼叫充电单次服务费',
  `if_open` tinyint(2) DEFAULT '1' COMMENT '是否对外开放。0：否；1：是。', --
  `if_emergency` tinyint(2) DEFAULT '0' COMMENT '是否应急场站 1:是 0 否',
  `del_flag` int(1) DEFAULT '0' COMMENT '删除标记 1：是 0: 否', --已删除场站不考虑
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `station_index` (`city_id`),
  KEY `normal_status` (`status`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=748 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='充电站基本信息表';

SET FOREIGN_KEY_CHECKS = 1;
