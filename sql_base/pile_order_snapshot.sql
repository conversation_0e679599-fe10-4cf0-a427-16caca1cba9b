/*
 Navicat Premium Dump SQL

 Source Server         : mysql_domi
 Source Server Type    : MySQL
 Source Server Version : 50744 (5.7.44-log)
 Source Host           : rm-uf6pi2e7rri610wi3mo.mysql.rds.aliyuncs.com:3306
 Source Schema         : chargepile-v3.0

 Target Server Type    : MySQL
 Target Server Version : 50744 (5.7.44-log)
 File Encoding         : 65001

 Date: 07/08/2025 10:37:02
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for pile_order_snapshot
-- ----------------------------
DROP TABLE IF EXISTS `pile_order_snapshot`;
CREATE TABLE `pile_order_snapshot` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `order_id` bigint(20) DEFAULT NULL COMMENT '订单id',
  `order_no` varchar(64) DEFAULT NULL COMMENT '订单编号',
  `operator_order_no` varchar(64) DEFAULT NULL COMMENT '运营商订单编号',
  `pay_type` tinyint(2) DEFAULT NULL COMMENT '支付类型。1：个人，2：企业',
  `pay_method` tinyint(2) DEFAULT '3' COMMENT '1、微信预支付 2、支付宝预支付 3、余额支付',
  `svcard_no` varchar(64) DEFAULT NULL COMMENT '储值卡编码',
  `user_phone` varchar(64) DEFAULT NULL COMMENT '用户手机号',
  `person_user_id` bigint(20) DEFAULT NULL COMMENT '个人用户id',
  `operator_id` varchar(64) DEFAULT NULL COMMENT '运营商id',
  `station_id` bigint(20) DEFAULT NULL COMMENT '场站ID',
  `station_name` varchar(255) DEFAULT NULL COMMENT '场站名称',
  `pile_no` varchar(64) DEFAULT NULL COMMENT '充电桩编号',
  `start_time` datetime DEFAULT NULL COMMENT '开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '完成时间',
  `timelong` float DEFAULT NULL COMMENT '时长',
  `start_soc` tinyint(3) DEFAULT NULL COMMENT '被充汽车的开始soc',
  `end_soc` tinyint(3) DEFAULT NULL COMMENT '被充汽车结束soc',
  `charge_power` float(8,4) DEFAULT NULL COMMENT '用电量',
  `charge_fees` float(8,4) DEFAULT NULL COMMENT '充电费',
  `server_fees` float(8,4) DEFAULT NULL COMMENT '服务费',
  `total_fees` float(8,2) DEFAULT NULL COMMENT '总费用',
  `status` tinyint(2) DEFAULT NULL COMMENT '订单状态:0:预约中 1:未接单 2:准备充电 3:充电中 4:充电完成 5:已取消',
  `overflow_policy` tinyint(2) DEFAULT NULL COMMENT '充满电策略',
  `normal_end` int(10) DEFAULT NULL,'是否正常结束 根据订单结算上报的情况来区分'
  `vin` varchar(64) DEFAULT NULL COMMENT '被充汽车车架号',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `add_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '增加时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `order_snapshot_normal_order_no` (`order_no`),
  KEY `order_snapshot_normal_station_id` (`station_id`),
  KEY `order_snapshot_normal_status` (`status`),
  KEY `order_snapshot_normal_operator_order_no` (`operator_order_no`),
  KEY `order_snapshot_normal_pile_no` (`pile_no`),
  KEY `order_snapshot_normal_start_time` (`start_time`),
  KEY `order_snapshot_normal_order_id` (`order_id`),
  KEY `order_snapshot_normal_operator_id` (`operator_id`),
  KEY `order_snapshot_normal_user_phone` (`user_phone`)
) ENGINE=InnoDB AUTO_INCREMENT=2764425 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='订单快照表';

SET FOREIGN_KEY_CHECKS = 1;
