/*
 Navicat Premium Dump SQL

 Source Server         : mysql_domi
 Source Server Type    : MySQL
 Source Server Version : 50744 (5.7.44-log)
 Source Host           : rm-uf6pi2e7rri610wi3mo.mysql.rds.aliyuncs.com:3306
 Source Schema         : chargepile-v3.0

 Target Server Type    : MySQL
 Target Server Version : 50744 (5.7.44-log)
 File Encoding         : 65001

 Date: 12/08/2025 15:16:20
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for sys_bus_log
-- ----------------------------
DROP TABLE IF EXISTS `sys_bus_log`;
CREATE TABLE `sys_bus_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `type` varchar(64) DEFAULT NULL COMMENT '业务操作类型', --update_device代表调动设备
  `bus_unique` varchar(128) DEFAULT NULL COMMENT '业务唯一值', -- 设备号，等同于pile_device的name
  `old_content` text COMMENT '旧内容', -- 旧场站名称，等同于pile_station的name
  `new_content` text COMMENT '新内容', -- 新场站名称，等同于pile_station的name
  `operator` varchar(64) DEFAULT NULL COMMENT '操作者',
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间', --即调动时间
  `connector_id` varchar(40) DEFAULT NULL COMMENT '合肥充电设备调运编码', -- 不使用
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=5970 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='业务数据操作日志';

SET FOREIGN_KEY_CHECKS = 1;
