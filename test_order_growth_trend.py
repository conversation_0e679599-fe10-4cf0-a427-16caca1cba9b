#!/usr/bin/env python3
"""
订单增长趋势计算测试脚本
基于六个月历史数据计算增长趋势
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from datetime import datetime, timedelta
from unittest.mock import Mock
import json

from app.core.analyzer import DataAnalyzer
from app.core.calculator import ScoreCalculator


def create_mock_historical_orders():
    """
    创建模拟的历史订单数据
    模拟六个月的订单数据，展示不同的增长趋势
    """
    scenarios = {
        "positive_growth": {
            "description": "正增长场景",
            "current_period_orders": 120,  # 当前期间120个订单
            "history_period_orders": 80,   # 历史期间80个订单
            "expected_growth": 50.0        # 预期增长50%
        },
        "negative_growth": {
            "description": "负增长场景", 
            "current_period_orders": 60,   # 当前期间60个订单
            "history_period_orders": 100,  # 历史期间100个订单
            "expected_growth": -40.0       # 预期下降40%
        },
        "no_growth": {
            "description": "零增长场景",
            "current_period_orders": 90,   # 当前期间90个订单
            "history_period_orders": 90,   # 历史期间90个订单
            "expected_growth": 0.0         # 预期增长0%
        },
        "from_zero": {
            "description": "从零开始场景",
            "current_period_orders": 50,   # 当前期间50个订单
            "history_period_orders": 0,    # 历史期间0个订单
            "expected_growth": 100.0       # 预期增长100%
        },
        "to_zero": {
            "description": "降至零场景",
            "current_period_orders": 0,    # 当前期间0个订单
            "history_period_orders": 80,   # 历史期间80个订单
            "expected_growth": -100.0      # 预期下降100%
        }
    }
    
    return scenarios


def test_order_growth_calculation():
    """测试订单增长趋势计算"""
    print("=" * 60)
    print("测试订单增长趋势计算")
    print("=" * 60)
    
    # 创建模拟数据库会话
    db_mock = Mock()
    analyzer = DataAnalyzer(db_mock)
    
    # 获取测试场景
    scenarios = create_mock_historical_orders()
    
    # 设置测试期间
    current_start = datetime(2025, 1, 1)
    current_end = datetime(2025, 1, 31)
    station_id = "station_test_001"
    
    print(f"测试场站: {station_id}")
    print(f"当前统计期: {current_start.strftime('%Y-%m-%d')} 到 {current_end.strftime('%Y-%m-%d')}")
    print(f"历史对比期: {(current_start - timedelta(days=31)).strftime('%Y-%m-%d')} 到 {(current_start - timedelta(days=1)).strftime('%Y-%m-%d')}")
    
    results = {}
    
    for scenario_name, scenario_data in scenarios.items():
        print(f"\n--- {scenario_data['description']} ---")
        
        # 模拟数据库查询返回
        def mock_query_side_effect(*args):
            mock_query = Mock()
            mock_query.filter = Mock(return_value=mock_query)
            
            # 模拟count()方法，根据查询条件返回不同的订单数量
            def mock_count():
                # 这里简化处理，实际应该根据时间范围判断
                # 假设第一次查询是当前期间，第二次是历史期间
                if not hasattr(mock_count, 'call_count'):
                    mock_count.call_count = 0
                mock_count.call_count += 1
                
                if mock_count.call_count == 1:
                    return scenario_data["current_period_orders"]
                else:
                    return scenario_data["history_period_orders"]
            
            mock_query.count = mock_count
            return mock_query
        
        db_mock.query.side_effect = mock_query_side_effect
        
        # 计算增长趋势
        growth_trend = analyzer._calculate_order_growth_trend(station_id, current_start, current_end)
        
        # 验证结果
        expected = scenario_data["expected_growth"]
        is_correct = abs(growth_trend - expected) < 0.01
        
        print(f"  当前期间订单数: {scenario_data['current_period_orders']}")
        print(f"  历史期间订单数: {scenario_data['history_period_orders']}")
        print(f"  计算增长率: {growth_trend}%")
        print(f"  预期增长率: {expected}%")
        print(f"  计算正确: {'✅' if is_correct else '❌'}")
        
        results[scenario_name] = {
            "calculated": growth_trend,
            "expected": expected,
            "correct": is_correct,
            "scenario": scenario_data
        }
        
        # 重置mock计数器
        if hasattr(db_mock.query.side_effect.__name__, 'call_count'):
            delattr(db_mock.query.side_effect, 'call_count')
    
    return results


def test_complete_workflow_with_growth():
    """测试包含增长趋势的完整工作流"""
    print("\n" + "=" * 60)
    print("测试包含增长趋势的完整工作流")
    print("=" * 60)
    
    # 创建模拟数据库会话
    db_mock = Mock()
    analyzer = DataAnalyzer(db_mock)
    
    # 创建模拟数据
    station = Mock()
    station.id = "station_test_001"
    station.name = "北京测试充电站"
    
    # 模拟当前期间的订单数据
    orders = []
    for i in range(100):  # 100个完成订单
        order = Mock()
        order.id = f"order_{i+1}"
        order.station_id = "station_test_001"
        order.person_user_id = f"person_{(i % 20) + 1}"
        order.total_fees = 65.0 + (i % 12) * 4
        order.charge_power = 48.0 + (i % 18) * 2
        order.timelong = 7500 + (i % 25) * 200
        order.status = 4
        order.normal_end = 1
        order.pay_type = 1
        order.create_time = datetime(2025, 1, 1) + timedelta(days=i % 30, hours=i % 24)
        orders.append(order)
    
    # 模拟设备数据
    devices = []
    for i in range(4):
        device = Mock()
        device.station_id = "station_test_001"
        device.name = f"device_{i+1:03d}"
        devices.append(device)
    
    # 配置数据库查询模拟
    query_call_count = 0
    def mock_query_side_effect(*args):
        nonlocal query_call_count
        query_call_count += 1
        
        mock_query = Mock()
        mock_query.filter = Mock(return_value=mock_query)
        mock_query.order_by = Mock(return_value=mock_query)
        mock_query.first = Mock(return_value=station)
        mock_query.all = Mock(return_value=[])  # 空的调运记录
        
        # 模拟订单增长趋势查询
        def mock_count():
            if query_call_count <= 2:  # 前两次查询是增长趋势计算
                return 100 if query_call_count == 1 else 75  # 当前100，历史75，增长33.33%
            else:
                return 100  # 其他查询返回100
        
        mock_query.count = mock_count
        return mock_query
    
    db_mock.query.side_effect = mock_query_side_effect
    
    # 创建完整测试数据
    raw_data = {
        "station": station,
        "devices": devices,
        "orders": orders,
        "period": {
            "start_date": datetime(2025, 1, 1),
            "end_date": datetime(2025, 1, 31),
            "days": 31
        }
    }
    
    # 计算各维度指标
    operation_metrics = analyzer.calculate_operation_metrics(raw_data)
    service_metrics = analyzer.calculate_service_metrics(raw_data)
    stability_metrics = analyzer.calculate_stability_metrics(raw_data)
    
    print("\n📊 包含增长趋势的运营效率指标:")
    for key, value in operation_metrics.items():
        print(f"  {key}: {value}")
    
    print("\n🎯 包含增长趋势的服务质量指标:")
    for key, value in service_metrics.items():
        print(f"  {key}: {value}")
    
    print("\n📈 包含增长趋势的稳定性指标:")
    for key, value in stability_metrics.items():
        print(f"  {key}: {value}")
    
    # 验证增长趋势计算
    expected_growth = ((100 - 75) / 75) * 100  # 33.33%
    actual_growth = stability_metrics.get("order_growth_trend", 0)
    
    print(f"\n🔍 增长趋势验证:")
    print(f"  当前期间订单: 100")
    print(f"  历史期间订单: 75")
    print(f"  预期增长率: {expected_growth:.2f}%")
    print(f"  实际增长率: {actual_growth}%")
    print(f"  计算正确: {'✅' if abs(actual_growth - expected_growth) < 0.1 else '❌'}")
    
    # 计算综合评分
    calculator = ScoreCalculator()
    score_result = calculator.calculate_total_score(
        operation_metrics,
        service_metrics,
        stability_metrics
    )
    
    print(f"\n🏆 包含增长趋势的综合评分:")
    print(f"  总分: {score_result['total_score']}")
    print(f"  等级: {score_result['grade']}")
    
    return {
        "operation_metrics": operation_metrics,
        "service_metrics": service_metrics,
        "stability_metrics": stability_metrics,
        "score_result": score_result,
        "growth_analysis": {
            "expected_growth": expected_growth,
            "actual_growth": actual_growth,
            "calculation_correct": abs(actual_growth - expected_growth) < 0.1
        }
    }


def main():
    """主函数"""
    print("🚀 开始订单增长趋势计算测试")
    print("基于六个月历史数据计算增长趋势")
    
    try:
        # 测试增长趋势计算
        growth_results = test_order_growth_calculation()
        
        # 测试完整工作流
        workflow_results = test_complete_workflow_with_growth()
        
        # 保存结果
        test_results = {
            "timestamp": datetime.now().isoformat(),
            "order_growth_implementation": {
                "description": "基于六个月历史数据计算订单增长趋势",
                "formula": "增长率 = (当前期间订单数 - 历史期间订单数) / 历史期间订单数 × 100%",
                "features": [
                    "自动计算历史对比期间",
                    "处理零订单的边界情况", 
                    "限制增长率在合理范围(-100%到+500%)",
                    "提供详细的增长分析"
                ]
            },
            "growth_calculation_tests": growth_results,
            "complete_workflow_test": workflow_results
        }
        
        with open("order_growth_test_results.json", "w", encoding="utf-8") as f:
            json.dump(test_results, f, ensure_ascii=False, indent=2)
        
        print(f"\n💾 订单增长趋势测试结果已保存到: order_growth_test_results.json")
        print("\n✅ 所有增长趋势测试完成！")
        
        # 统计测试结果
        all_correct = all(result["correct"] for result in growth_results.values())
        workflow_correct = workflow_results["growth_analysis"]["calculation_correct"]
        
        print(f"\n📊 测试总结:")
        print(f"  增长趋势场景测试: {'✅ 全部通过' if all_correct else '❌ 部分失败'}")
        print(f"  完整工作流测试: {'✅ 通过' if workflow_correct else '❌ 失败'}")
        print(f"  总体结果: {'✅ 成功' if all_correct and workflow_correct else '❌ 失败'}")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
