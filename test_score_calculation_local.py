#!/usr/bin/env python3
"""
本地评分计算测试脚本
用于验证评分算法的计算结果
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from datetime import datetime, timedelta
from unittest.mock import Mock
import json

from app.core.analyzer import DataAnalyzer
from app.core.calculator import ScoreCalculator


def create_mock_station():
    """创建模拟场站数据"""
    station = Mock()
    station.id = "station_test_001"
    station.name = "测试充电站"
    station.city_name = "北京市"
    return station


def create_mock_devices():
    """创建模拟设备数据"""
    devices = []
    base_date = datetime(2025, 1, 1)
    
    for i in range(4):  # 4个设备
        device = Mock()
        device.id = f"device_{i+1}"
        device.station_id = "station_test_001"
        device.install_date = base_date + timedelta(days=i*10)  # 设备逐步安装
        device.remove_date = None  # 未移除
        devices.append(device)
    
    return devices


def create_mock_orders():
    """创建模拟订单数据"""
    orders = []
    base_date = datetime(2025, 1, 1)
    
    # 创建100个订单，分布在30天内
    for i in range(100):
        order = Mock()
        order.id = f"order_{i+1}"
        order.station_id = "station_test_001"
        order.user_id = f"user_{i % 20 + 1}"  # 20个不同用户
        order.total_fees = 50.0 + (i % 10) * 5  # 50-95元不等
        order.charge_power = 40.0 + (i % 15) * 2  # 40-68kWh不等
        order.timelong = 7200 + (i % 20) * 300  # 2-3小时不等（秒）
        order.status = 4  # 充电完成
        order.normal_end = 1 if i % 10 != 9 else 0  # 90%正常结束
        order.pay_type = 1 if i % 5 != 4 else 2  # 80%个人用户
        order.create_time = base_date + timedelta(days=i % 30, hours=i % 24)
        orders.append(order)
    
    return orders


def create_mock_raw_data():
    """创建完整的模拟原始数据"""
    station = create_mock_station()
    devices = create_mock_devices()
    orders = create_mock_orders()
    
    start_date = datetime(2025, 1, 1)
    end_date = datetime(2025, 1, 31)
    
    return {
        "station": station,
        "devices": devices,
        "orders": orders,
        "users": [],
        "period": {
            "start_date": start_date,
            "end_date": end_date,
            "days": 31
        }
    }


def test_data_analyzer():
    """测试数据分析器"""
    print("=" * 60)
    print("测试数据分析器")
    print("=" * 60)
    
    # 创建模拟数据库会话
    db_mock = Mock()
    analyzer = DataAnalyzer(db_mock)
    
    # 模拟数据库查询返回
    def mock_query_side_effect(*args):
        mock_query = Mock()
        mock_query.filter.return_value = mock_query
        mock_query.all.return_value = []  # 返回空列表，避免实际查询
        return mock_query
    
    db_mock.query.side_effect = mock_query_side_effect
    
    # 创建测试数据
    raw_data = create_mock_raw_data()
    
    # 计算各维度指标
    operation_metrics = analyzer.calculate_operation_metrics(raw_data)
    service_metrics = analyzer.calculate_service_metrics(raw_data)
    stability_metrics = analyzer.calculate_stability_metrics(raw_data)
    
    print("\n📊 运营效率指标:")
    for key, value in operation_metrics.items():
        print(f"  {key}: {value}")
    
    print("\n🎯 服务质量指标:")
    for key, value in service_metrics.items():
        print(f"  {key}: {value}")
    
    print("\n📈 稳定性指标:")
    for key, value in stability_metrics.items():
        print(f"  {key}: {value}")
    
    return operation_metrics, service_metrics, stability_metrics


def test_score_calculator(operation_metrics, service_metrics, stability_metrics):
    """测试评分计算器"""
    print("\n" + "=" * 60)
    print("测试评分计算器")
    print("=" * 60)
    
    calculator = ScoreCalculator()
    
    # 计算综合评分
    score_result = calculator.calculate_total_score(
        operation_metrics,
        service_metrics,
        stability_metrics
    )
    
    print(f"\n🏆 综合评分结果:")
    print(f"  总分: {score_result['total_score']}")
    print(f"  等级: {score_result['grade']}")
    
    print(f"\n📊 维度评分:")
    dimension_scores = score_result['dimension_scores']
    print(f"  运营效率: {dimension_scores['operation_score']}")
    print(f"  服务质量: {dimension_scores['service_score']}")
    print(f"  稳定性: {dimension_scores['stability_score']}")
    
    return score_result


def test_batch_calculation():
    """测试批量计算"""
    print("\n" + "=" * 60)
    print("测试批量计算")
    print("=" * 60)
    
    calculator = ScoreCalculator()
    
    # 创建3个场站的测试数据
    stations_metrics = []
    for i in range(3):
        station_data = {
            "station_id": f"station_{i+1}",
            "operation_metrics": {
                "daily_revenue_rate": 1000 + i * 200,
                "device_utilization": 0.6 + i * 0.1,
                "revenue_per_device": 2000 + i * 300,
                "completion_rate": 85 + i * 5
            },
            "service_metrics": {
                "avg_charge_time": 7200 + i * 600,
                "avg_charge_power": 45 + i * 5,
                "user_retention_rate": 40 + i * 15,
                "failure_rate": 15 - i * 3
            },
            "stability_metrics": {
                "order_growth_trend": i * 8,
                "revenue_stability": 75 + i * 8,
                "operation_continuity": 80 + i * 10,
                "device_health": 95 + i * 2,
                "time_balance": 60 + i * 15
            }
        }
        stations_metrics.append(station_data)
    
    # 批量计算评分
    results = calculator.batch_calculate_scores(stations_metrics)
    
    print(f"\n🏆 批量评分结果:")
    for i, result in enumerate(results):
        print(f"\n场站 {i+1} ({result['station_id']}):")
        print(f"  总分: {result['total_score']}")
        print(f"  等级: {result['grade']}")
        print(f"  运营效率: {result['dimension_scores']['operation_score']}")
        print(f"  服务质量: {result['dimension_scores']['service_score']}")
        print(f"  稳定性: {result['dimension_scores']['stability_score']}")
    
    return results


def save_results_to_file(operation_metrics, service_metrics, stability_metrics, score_result, batch_results):
    """保存结果到文件"""
    results = {
        "timestamp": datetime.now().isoformat(),
        "single_station_test": {
            "operation_metrics": operation_metrics,
            "service_metrics": service_metrics,
            "stability_metrics": stability_metrics,
            "score_result": score_result
        },
        "batch_calculation_test": batch_results
    }
    
    with open("score_calculation_results.json", "w", encoding="utf-8") as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    
    print(f"\n💾 结果已保存到: score_calculation_results.json")


def main():
    """主函数"""
    print("🚀 开始评分算法本地测试")
    
    try:
        # 测试数据分析器
        operation_metrics, service_metrics, stability_metrics = test_data_analyzer()
        
        # 测试评分计算器
        score_result = test_score_calculator(operation_metrics, service_metrics, stability_metrics)
        
        # 测试批量计算
        batch_results = test_batch_calculation()
        
        # 保存结果
        save_results_to_file(operation_metrics, service_metrics, stability_metrics, score_result, batch_results)
        
        print("\n✅ 所有测试完成！")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
