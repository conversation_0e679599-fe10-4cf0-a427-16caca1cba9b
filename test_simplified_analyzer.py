#!/usr/bin/env python3
"""
简化版数据分析器测试脚本
基于当前设备数量和调运记录做简单加减法
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from datetime import datetime, timedelta
from unittest.mock import Mock
import json

from app.core.analyzer import DataAnalyzer
from app.core.calculator import ScoreCalculator


def create_mock_simplified_bus_logs():
    """
    创建简化的设备调运记录
    模拟现实中不频繁的调运情况
    """
    logs = []
    base_date = datetime(2025, 1, 1)
    station_name = "北京测试充电站"
    
    # 模拟统计期内的少量调运记录
    log_data = [
        {"date": base_date + timedelta(days=10), "device": "device_004", "old": "上海充电站", "new": station_name},  # 调入1台
        {"date": base_date + timedelta(days=20), "device": "device_002", "old": station_name, "new": "天津充电站"},  # 调出1台
    ]
    
    for i, log_info in enumerate(log_data):
        log = Mock()
        log.id = i + 1
        log.type = "update_device"
        log.bus_unique = log_info["device"]
        log.old_content = log_info["old"]
        log.new_content = log_info["new"]
        log.create_time = log_info["date"]
        log.operator = "admin"
        logs.append(log)
    
    return logs


def test_simplified_device_count():
    """测试简化的设备数量计算"""
    print("=" * 60)
    print("测试简化的设备数量计算")
    print("=" * 60)
    
    # 创建模拟数据库会话
    db_mock = Mock()
    analyzer = DataAnalyzer(db_mock)
    
    # 创建模拟场站
    station = Mock()
    station.id = "station_test_001"
    station.name = "北京测试充电站"
    
    # 创建设备调运记录
    bus_logs = create_mock_simplified_bus_logs()
    
    # 模拟数据库查询
    def mock_query_side_effect(*args):
        mock_query = Mock()
        mock_query.filter = Mock(return_value=mock_query)
        mock_query.order_by = Mock(return_value=mock_query)
        mock_query.first = Mock(return_value=station)
        mock_query.all = Mock(return_value=bus_logs)
        return mock_query
    
    db_mock.query.side_effect = mock_query_side_effect
    
    # 创建模拟设备数据
    devices = []
    for i in range(5):  # 当前5个设备
        device = Mock()
        device.station_id = "station_test_001"
        device.name = f"device_{i+1:03d}"
        devices.append(device)
    
    # 测试设备数量计算
    start_date = datetime(2025, 1, 1)
    end_date = datetime(2025, 1, 31)
    
    print(f"场站名称: {station.name}")
    print(f"当前设备数量: {len(devices)}")
    print(f"统计期间: {start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')}")
    
    tt_days = ((end_date - start_date).days + 1)*len(devices)
    print("\n设备调运记录:")
    days_in = 0 # 查询到调出的设备，需要增加天数
    days_out = 0 #查询到调入的设备，需要减少天数
    devices_in=0
    devices_out=0
    for log in bus_logs:
        if log.new_content == station.name:
            direction = "调入"
            devices_in+=1
            days_out += (log.create_time - start_date).days
        else:
            direction = "调出"
            devices_out+=1
            days_in += (log.create_time - start_date).days
        print(f"  {log.create_time.strftime('%Y-%m-%d')}: {log.bus_unique} {direction}")

    
    
    avg_device_count = analyzer._calculate_average_device_count(devices, start_date, end_date)
    
    print(f"\n简化计算逻辑:")
    print(f"  当前设备数量: {len(devices)}")
    print(f"  调入设备数: {devices_in},计算天数: {days_out} ")
    print(f"  调出设备数: {devices_out},计算天数：{days_in}")
    print(f"  影响天数: {days_in - days_out}")
    print(f"  总天数: {tt_days}")
    print(f"  平均设备数量: {tt_days} + {days_in - days_out} = {avg_device_count}*{((end_date - start_date).days + 1)}")
    
    # 手动验证
    expected = (tt_days + days_in - days_out)/((end_date - start_date).days + 1)
    print(f"\n验证结果:")
    print(f"  计算结果: {avg_device_count}")
    print(f"  预期结果: {expected}")
    print(f"  计算正确: {'✅' if abs(avg_device_count - expected) < 0.01 else '❌'}")
    
    return avg_device_count


def test_different_scenarios():
    """测试不同调运场景"""
    print("\n" + "=" * 60)
    print("测试不同调运场景")
    print("=" * 60)
    
    scenarios = [
        {
            "name": "无调运记录",
            "current_devices": 4,
            "devices_in": 0,
            "devices_out": 0,
            "expected": 4.0
        },
        {
            "name": "只有调入",
            "current_devices": 3,
            "devices_in": 2,
            "devices_out": 0,
            "expected": 3 + 2 * 0.5
        },
        {
            "name": "只有调出",
            "current_devices": 5,
            "devices_in": 0,
            "devices_out": 1,
            "expected": 5 + (-1) * 0.5
        },
        {
            "name": "调入调出平衡",
            "current_devices": 4,
            "devices_in": 2,
            "devices_out": 2,
            "expected": 4.0
        },
        {
            "name": "调入大于调出",
            "current_devices": 3,
            "devices_in": 3,
            "devices_out": 1,
            "expected": 3 + 2 * 0.5
        }
    ]
    
    for scenario in scenarios:
        print(f"\n场景: {scenario['name']}")
        print(f"  当前设备: {scenario['current_devices']}")
        print(f"  调入: {scenario['devices_in']}, 调出: {scenario['devices_out']}")
        
        # 计算结果
        net_change = scenario['devices_in'] - scenario['devices_out']
        result = scenario['current_devices'] + net_change * 0.5
        
        print(f"  计算: {scenario['current_devices']} + ({net_change}) * 0.5 = {result}")
        print(f"  预期: {scenario['expected']}")
        print(f"  正确: {'✅' if abs(result - scenario['expected']) < 0.01 else '❌'}")


def test_complete_simplified_workflow():
    """测试完整的简化工作流"""
    print("\n" + "=" * 60)
    print("测试完整的简化工作流")
    print("=" * 60)
    
    # 创建模拟数据库会话
    db_mock = Mock()
    analyzer = DataAnalyzer(db_mock)
    
    # 创建模拟数据
    station = Mock()
    station.id = "station_test_001"
    station.name = "北京测试充电站"
    
    bus_logs = create_mock_simplified_bus_logs()
    
    # 模拟订单数据
    orders = []
    for i in range(90):  # 90个完成订单
        order = Mock()
        order.id = f"order_{i+1}"
        order.station_id = "station_test_001"
        order.person_user_id = f"person_{(i % 18) + 1}"  # 18个不同用户
        order.total_fees = 55.0 + (i % 10) * 6
        order.charge_power = 42.0 + (i % 15) * 4
        order.timelong = 6800 + (i % 20) * 400
        order.status = 4
        order.normal_end = 1
        order.pay_type = 1
        order.create_time = datetime(2025, 1, 1) + timedelta(days=i % 30, hours=i % 24)
        orders.append(order)
    
    # 配置数据库查询模拟
    def mock_query_side_effect(*args):
        mock_query = Mock()
        mock_query.filter = Mock(return_value=mock_query)
        mock_query.order_by = Mock(return_value=mock_query)
        mock_query.first = Mock(return_value=station)
        mock_query.all = Mock(return_value=bus_logs)
        return mock_query
    
    db_mock.query.side_effect = mock_query_side_effect
    
    # 创建设备数据
    devices = []
    for i in range(5):  # 5个设备
        device = Mock()
        device.station_id = "station_test_001"
        device.name = f"device_{i+1:03d}"
        devices.append(device)
    
    # 创建完整测试数据
    raw_data = {
        "station": station,
        "devices": devices,
        "orders": orders,
        "period": {
            "start_date": datetime(2025, 1, 1),
            "end_date": datetime(2025, 1, 31),
            "days": 31
        }
    }
    
    # 计算各维度指标
    operation_metrics = analyzer.calculate_operation_metrics(raw_data)
    service_metrics = analyzer.calculate_service_metrics(raw_data)
    stability_metrics = analyzer.calculate_stability_metrics(raw_data)
    
    print("\n📊 简化后的运营效率指标:")
    for key, value in operation_metrics.items():
        print(f"  {key}: {value}")
    
    print("\n🎯 简化后的服务质量指标:")
    for key, value in service_metrics.items():
        print(f"  {key}: {value}")
    
    print("\n📈 简化后的稳定性指标:")
    for key, value in stability_metrics.items():
        print(f"  {key}: {value}")
    
    # 计算综合评分
    calculator = ScoreCalculator()
    score_result = calculator.calculate_total_score(
        operation_metrics,
        service_metrics,
        stability_metrics
    )
    
    print(f"\n🏆 简化后的综合评分:")
    print(f"  总分: {score_result['total_score']}")
    print(f"  等级: {score_result['grade']}")
    
    return {
        "operation_metrics": operation_metrics,
        "service_metrics": service_metrics,
        "stability_metrics": stability_metrics,
        "score_result": score_result,
        "simplification_summary": {
            "current_devices": len(devices),
            "devices_in": 1,
            "devices_out": 1,
            "net_change": 0,
            "average_devices": len(devices)
        }
    }


def main():
    """主函数"""
    print("🚀 开始简化版评分算法测试")
    print("基于当前设备数量和调运记录做简单加减法")
    
    try:
        # 测试简化的设备数量计算
        avg_device_count = test_simplified_device_count()
        
        # 测试不同场景
        test_different_scenarios()
        
        # 测试完整工作流
        results = test_complete_simplified_workflow()
        
        # 保存结果
        simplified_results = {
            "timestamp": datetime.now().isoformat(),
            "simplification_approach": {
                "method": "基于当前设备数量和调运记录做加减法",
                "formula": "平均设备数 = 当前设备数 + (调入数 - 调出数) * 0.5",
                "assumption": "调运在统计期中间发生，影响一半的时间",
                "advantages": [
                    "计算简单高效",
                    "适合现实中不频繁的调运情况",
                    "易于理解和维护",
                    "性能优秀"
                ]
            },
            "test_results": {
                "average_device_count": avg_device_count,
                "complete_workflow": results
            }
        }
        
        with open("simplified_test_results.json", "w", encoding="utf-8") as f:
            json.dump(simplified_results, f, ensure_ascii=False, indent=2)
        
        print(f"\n💾 简化测试结果已保存到: simplified_test_results.json")
        print("\n✅ 所有简化测试完成！")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
